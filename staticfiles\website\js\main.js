function setActiveNavLink(event) {
    event.preventDefault();
    
    const navLinks = document.querySelectorAll('.nav-link');
    navLinks.forEach((link) => {
        link.classList.remove('active');
    });

    event.target.classList.add('active');
}

document.addEventListener('DOMContentLoaded', function () {
    const navLinks = document.querySelectorAll('.nav-link');
    navLinks.forEach((link) => {
        link.addEventListener('click', setActiveNavLink);
    });
});
