from django.urls import path 
from . import views

urlpatterns = [
    path("", views.home , name="home"),
    path("accounts/", views.accounts, name="accounts"),
    path("story/", views.story, name="story"),
    path("accounts/add_account_page/", views.add_account_page, name="add-account-page"),
    path("accounts/add_account/", views.add_account, name="add-account"),
    path("accounts/edit_account/<int:id>", views.edit_account, name="edit-account"),
    path("accounts/delete_account/<int:id>", views.delete_account, name="delete-account"),
    path("accounts/get_account/<int:id>", views.get_account, name="get-account"),
    path("generate_story/", views.create_story, name="create-story"),
    path("generate_entry/<int:id>", views.generate_entry, name="generate-entry"),
    path("set_podcast_category/<int:id>", views.set_podcast_category, name="set-podcast-category"),
    
    path("history/", views.history, name="history"),


    path("delete_podcast_entry/<int:id>", views.delete_podcast_entry, name="delete-pod-entry"),


    path("add_pod_entry/<int:id>", views.add_pod_entry, name="add-pod-entry"),
    path("duplicate_entry/<int:id>", views.duplicate_entry, name="duplicate-entry"),
    path("entry_process_status/<int:id>", views.check_entry_processing_status, name="entry-process-status"),

    path("get_clip_meta/", views.get_clip_meta, name="get-clip-meta"),
    path("get_clip_meta/<int:id>", views.get_clip_meta, name="get-clip-meta-with-id"),
    path("modify_pod_entry/<int:id>", views.modify_pod_entry, name="modify-pod-entry"),

    path("swithc_m_1/", views.switch_to_model_1, name="switch-m-i"),
    path("swithc_m_2/", views.switch_to_model_2, name="switch-m-ii"),
    path("swithc_m_3/", views.switch_to_model_3, name="switch-m-iii"),
]
