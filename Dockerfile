# Use the official Python image from the Docker Hub
FROM python:3.10.12-slim


# Set the working directory to /app
WORKDIR /app


RUN apt-get update && apt-get install -y imagemagick

# Set ImageMagick binary path environment variable
ENV IMAGEMAGICK_BINARY=/usr/bin/convert

# Copy the requirements file
COPY requirements.txt /app/

# Install any dependencies
RUN pip install --no-cache-dir -r requirements.txt

# Copy the current directory contents into the container at /app
COPY . /app/

# Expose port 8000 for the Django app
EXPOSE 8000

# Run the Django development server
CMD ["python", "manage.py", "runserver", "0.0.0.0:8000"]
