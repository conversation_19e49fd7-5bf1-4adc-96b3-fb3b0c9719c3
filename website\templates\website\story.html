{% extends "base.html" %}

{% block content %}
{% include "partials/nav.html" %}

    <div class="container text-light">
        <h1 class="text-center ">Generate your story now </h1>

        <div>
            
            <form hx-post="/generate_story/" hx-indicator="#waiting" hx-swap="none" class="custom-form mt-4 pt-2 mb-lg-0 mb-5 w-75 mx-auto" role="search" id="story" name="story">
                <div class="text-center text-white mb-4 d-flex flex-column justify-content-around">
                        <h2 class="py-2 ">Story Title</h2>
                        <input type="text" required="" name="story-title" class="py-2 fs-4 rounded-pill text-center px-4">
                        <h3 class="py-2">Category</h3>  
                        <select name="category" class=" py-3 fs-4 text-center bg-danger border-danger text-white fw-bold rounded-pill">
                            
                            
                            <option value="Motivational">Motivational</option>
                            
                            
                            <option value="Stories">Stories</option>
                            
                        </select>
                </div>
                <div class="input-group input-group-lg  mx-auto">
                    
                    <div class="form-floating text-center" id="story-form">
                        <textarea class="form-control fw-bold fs-3 mx-auto" for="story" name="story" placeholder="Leave a comment here" required="" id="floatingTextarea2" style="height: 320px"></textarea>
                        <button type="submit" class="trans-focus p-0 form-control btn btn-outline-danger fw-bold fs-4 text-white mx-auto border-5">Generate Story</button>
                    </div>
                    
                </div>
                
            </form>     
        </div>
    </div>

{% endblock content %}