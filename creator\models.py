from django.db import models
from django.conf import settings
import os

class UserProfile(models.Model):
    username = models.CharField(max_length=50)
    category = models.ForeignKey('Category', on_delete=models.DO_NOTHING, null=True)
    logo = models.ImageField(upload_to='user_logos/', null=True, blank=True)
    created = models.DateTimeField(auto_now_add=True)
    updated = models.DateTimeField(auto_now=True)
    
    def __str__(self):
        return self.username

class Category(models.Model):
    name = models.CharField(max_length=100)
    
    class Meta:
        verbose_name = 'Category'  # Singular name
        verbose_name_plural = 'Categories'

    def __str__(self):
        return self.name
    
    def create_directory_structure(self):
        # Define the base directory path using MEDIA_ROOT
        base_dir = os.path.join(settings.MEDIA_ROOT, 'creator', 'category', self.name)

        # Define subdirectories
        subdirectories = ['logos', 'videos', 'output']

        # Check if the base directory exists before creating
        if not os.path.exists(base_dir):
            os.makedirs(base_dir)

        # Create subdirectories
        for subdirectory in subdirectories:
            subdirectory_path = os.path.join(base_dir, subdirectory)
            if not os.path.exists(subdirectory_path):
                os.makedirs(subdirectory_path)

    def save(self, *args, **kwargs):
        # Call the original save method
        super().save(*args, **kwargs)

        # Create the directory structure
        self.create_directory_structure()
    

    
class Story(models.Model):
    title = models.CharField(max_length=200)
    category = models.ForeignKey(Category, on_delete=models.SET_NULL, null=True, blank=True)
    script = models.TextField(blank=True)
    video_url = models.URLField(blank=True)
    created = models.DateTimeField(auto_now_add=True)  # Automatically set when the story is created
    updated = models.DateTimeField(auto_now=True)
    
    def __str__(self):
        return self.title
    
    class Meta:
        verbose_name = 'Story'  # Singular name
        verbose_name_plural = 'Stories'
