#!/usr/bin/env python
"""
Test runner for creator module video processing functionality.

This script sets up the Django environment and runs the comprehensive
test suite for the creator module.
"""

import os
import sys
import django
from django.conf import settings
from django.test.utils import get_runner

if __name__ == "__main__":
    # Set up Django environment
    os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'gg.settings')
    django.setup()
    
    # Get the Django test runner
    TestRunner = get_runner(settings)
    test_runner = TestRunner()
    
    # Run the creator tests
    failures = test_runner.run_tests(["creator.test_video_processing"])
    
    if failures:
        sys.exit(1)
    else:
        print("\n✅ All creator module tests passed successfully!")
        sys.exit(0)
