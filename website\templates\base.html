{% load static %}
<!doctype html>
<html lang="en">
  <head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">

    <link rel="icon" href="{% static "website/images/logo.png" %}">
    <link href="{% static "website/css/bootstrap.min.css" %}" rel="stylesheet">
    <link href="{% static "website/css/style.css" %}" rel="stylesheet">
    
  </head>
  <body class="fw-bold">
    {% block content %}
    {% endblock content %}
    
    <script src="https://unpkg.com/htmx.org@1.9.8"></script>
   
    <script>
      
      document.body.addEventListener('htmx:configRequest', (event) => {
        event.detail.headers['X-CSRFToken'] = '{{ csrf_token }}';
      })
    </script>
    <script src="{% static "website/js/main.js" %}"></script>
    <script src="{% static "website/js/bootstrap.bundle.js" %}" ></script>

  </body>
</html>
