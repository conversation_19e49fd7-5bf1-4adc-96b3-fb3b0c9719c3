{% extends "base.html" %}

{% block content %}
    {% include "partials/nav.html" %}
    <div class="container ">
        <div class="text-center text-white">
            <h3>Add Account</h3>
        </div>
        <form action="{% url "website:add-account" %}" enctype="multipart/form-data" method="post" class="row text-center mx-auto text-white w-50 g-3  rounded py-3">
            {% csrf_token %}
            <div class="">
              <label class="" for="inlineFormInputGroupUsername">Username</label>
              <div class="input-group">
                <div class="input-group-text">@</div>
                <input type="text" class="form-control" required name="username" id="inlineFormInputGroupUsername" placeholder="Username">
              </div>
            </div>
          
            <div class="">
              <label class="" for="inlineFormSelectPref">Category</label>
              <select class="form-select" id="inlineFormSelectPref" name="category" required>
                <option disabled></option>
                {% for category in categories %}
                    <option value="{{category.name}}">{{category.name}}</option>
                {% endfor %}
              </select>
            </div>
          
            <div class="">
                <div class="mb-3">
                    <label for="formFile" class="form-label">Logo</label>
                    <input class="form-control" type="file" id="formFile" required name="logo">
                  </div>
            </div>
            
            <div class="col-12">
              <button type="submit" class="btn btn-outline-light mx-auto">Submit</button>
            </div>
          </form>
    </div>
{% endblock content %}