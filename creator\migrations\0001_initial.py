# Generated by Django 4.2.5 on 2023-09-18 00:22

from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = []

    operations = [
        migrations.CreateModel(
            name="Story",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("title", models.<PERSON>r<PERSON><PERSON>(blank=True, max_length=255)),
                ("text", models.TextField(blank=True)),
                ("subtitles", models.TextField(blank=True)),
                ("posted", models.BooleanField(default=False)),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                ("updated_at", models.DateTimeField(auto_now=True)),
            ],
        ),
    ]
