<tr id="pod-entry-{{entry.id}}" >
    <td>#{{entry.id}}</td>
    <td><input type="text" hx-post="{% url "website:modify-pod-entry" entry.id%}" name="start" class="w-50 rounded bg-dark text-center border-white   text-white fw-bolder" {% if entry.start_time %}value="{{entry.formatted_start_time}}"{% endif %}></td>
    <td><input type="text" hx-post="{% url "website:modify-pod-entry" entry.id%}" name="end" class="w-50 rounded bg-dark text-center border-white text-white fw-bolder" {% if entry.end_time %}value="{{entry.formatted_end_time}}"{% endif %}></td>
    <td>
      {% if entry.transcripe %}
          <input class="form-check-input" type="checkbox" id="flexSwitchCheckDefault" hx-post="{% url "website:modify-pod-entry" entry.id%}" name="transcripe" class="rounded bg-dark text-center border-white" checked>
      {% else %}
          <input class="form-check-input" type="checkbox" id="flexSwitchCheckDefault" hx-post="{% url "website:modify-pod-entry" entry.id%}" name="transcripe" class="rounded bg-dark text-center border-white">
      {% endif %}

    </td>

    

    <td><label class="badge badge-danger">{{entry.completed}}</label></td>
    <td><button hx-post="{% url "website:delete-pod-entry" entry.id %}" hx-target="#pod-entry-{{entry.id}}" class="btn btn-danger fw-bolder rounded">X</button></td>
    <td>
      {% if entry.completed %}
      <a  href="{{entry.get_first_location}}" target="_blank" class="btn btn-outline-secondary rounded-pill border-2 text-white fw-bolder mx-auto">View</a>

      {% elif entry.processing %}
      <svg id="status-container" hx-get="{% url "website:entry-process-status" entry.id %}" hx-trigger="every 3s" hx-target="this" fill="#e5a50a" class="spinning" version="1.1" id="Layer_1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" viewBox="0 0 32 32" xml:space="preserve" width="64px" height="64px">

          <g id="SVGRepo_bgCarrier" stroke-width="0"/>
          
          <g id="SVGRepo_tracerCarrier" stroke-linecap="round" stroke-linejoin="round"/>
          
          <g id="SVGRepo_iconCarrier"> <path id="process_1_" d="M17.38,23.36h-2.76c-0.199,0-0.36-0.161-0.36-0.36v-1.567l-0.869-0.363l-1.111,1.111 c-0.067,0.067-0.159,0.105-0.254,0.105l0,0c-0.096,0-0.187-0.038-0.254-0.105L9.82,20.229c-0.141-0.141-0.141-0.369,0-0.51 l1.108-1.107l-0.357-0.871H9c-0.199,0-0.36-0.161-0.36-0.36v-2.76c0-0.199,0.161-0.36,0.36-0.36h1.567l0.363-0.869L9.82,12.28 c-0.14-0.141-0.14-0.368,0-0.509l1.951-1.952c0.135-0.136,0.374-0.136,0.509,0l1.108,1.109l0.872-0.357V9 c0-0.199,0.161-0.36,0.36-0.36h2.76c0.199,0,0.36,0.161,0.36,0.36v1.567l0.868,0.363l1.11-1.111c0.135-0.135,0.375-0.136,0.51,0 l1.952,1.952c0.067,0.067,0.105,0.159,0.105,0.254c0,0.096-0.038,0.187-0.105,0.254l-1.109,1.108l0.357,0.872H23 c0.199,0,0.36,0.161,0.36,0.36v2.76c0,0.199-0.161,0.36-0.36,0.36h-1.567l-0.363,0.868l1.111,1.11 c0.067,0.067,0.105,0.159,0.105,0.255s-0.038,0.188-0.105,0.255l-1.952,1.952c-0.067,0.067-0.159,0.105-0.255,0.105l0,0 c-0.096,0-0.188-0.038-0.255-0.105l-1.107-1.109l-0.871,0.357V23C17.74,23.199,17.579,23.36,17.38,23.36z M14.98,22.64h2.04v-1.452 c0-0.146,0.089-0.277,0.224-0.333l1.316-0.54c0.134-0.057,0.288-0.024,0.392,0.078l1.022,1.024l1.443-1.443l-1.028-1.026 c-0.103-0.104-0.134-0.259-0.077-0.394l0.55-1.312c0.056-0.134,0.187-0.222,0.332-0.222h1.446v-2.04h-1.452 c-0.146,0-0.277-0.088-0.333-0.223l-0.54-1.316c-0.055-0.134-0.024-0.289,0.078-0.391l1.024-1.023l-1.443-1.443l-1.026,1.027 c-0.104,0.103-0.259,0.135-0.394,0.078l-1.312-0.549c-0.134-0.056-0.222-0.187-0.222-0.332V9.36h-2.04v1.452 c0,0.146-0.088,0.278-0.223,0.333l-1.316,0.54c-0.135,0.054-0.288,0.024-0.391-0.079l-1.023-1.023l-1.442,1.443l1.027,1.028 c0.103,0.103,0.134,0.259,0.077,0.394L11.14,14.76c-0.056,0.134-0.187,0.221-0.332,0.221H9.36v2.04h1.452 c0.146,0,0.278,0.089,0.333,0.224l0.54,1.316c0.055,0.135,0.024,0.289-0.079,0.392l-1.022,1.022l1.442,1.443l1.028-1.028 c0.104-0.104,0.259-0.133,0.394-0.077l1.312,0.55c0.134,0.056,0.221,0.187,0.221,0.332L14.98,22.64L14.98,22.64z M16,19.36 c-1.853,0-3.36-1.508-3.36-3.36s1.507-3.36,3.36-3.36s3.36,1.507,3.36,3.36S17.853,19.36,16,19.36z M16,13.36 c-1.456,0-2.64,1.185-2.64,2.64c0,1.456,1.185,2.64,2.64,2.64c1.456,0,2.64-1.184,2.64-2.64S17.456,13.36,16,13.36z M16,31.36 c-4.529,0-8.716-1.933-11.64-5.338V30H3.64v-5.36H9v0.721H4.743C7.536,28.725,11.598,30.64,16,30.64 c8.072,0,14.64-6.567,14.64-14.64h0.721C31.36,24.47,24.47,31.36,16,31.36z M1.36,16H0.64C0.64,7.53,7.53,0.64,16,0.64 c4.529,0,8.716,1.933,11.64,5.337V2h0.721v5.36H23V6.64h4.257C24.464,3.275,20.401,1.36,16,1.36C7.927,1.36,1.36,7.927,1.36,16z"/> <rect id="_Transparent_Rectangle" style="fill:none;" width="32" height="32"/> </g>
          
      </svg>
      {% else %}
      {% if podcast.category %}
      
      <svg id="podGenerateButton" hx-swap="outerHTML" hx-post="{% url 'website:generate-entry' entry.id %}" hx-trigger="click" style="cursor: pointer;" width="48px" height="48px" viewBox="0 0 256 256" xmlns="http://www.w3.org/2000/svg" fill="#000000" class="pe-auto" >
          
          <g id="SVGRepo_bgCarrier" stroke-width="0"/>
          
          <g id="SVGRepo_tracerCarrier" stroke-linecap="round" stroke-linejoin="round"/>
          
          <g id="SVGRepo_iconCarrier"> <g> 
              <path d="m195.72 238.09c-3.5 2.24-138 1.79-141 0s-2.72-23.27 0-25.09 137.78-2.46 141 0 3.5 22.86 0 25.09z" fill="#191919"/> 
              <path d="m133.17 247.61h-8.17c-17.77 0-34.7-.19-47.66-.44-23.88-.47-24.66-.95-26.77-2.22-4-2.43-5.75-7.44-6-17.28-.37-17.27 3.86-20.09 5.67-21.29 2.23-1.48 2.9-1.93 27-2.44 12.89-.27 30.29-.45 47.74-.49 18.39 0 35.39.06 47.86.31 24.49.48 25.37 1.14 27.71 2.93 5.23 4 5.94 14.12 5.62 21.92-.36 8.74-2.26 13.75-6.16 16.24-2.3 1.47-4.32 2.76-66.84 2.76zm-72.41-16.91c21.68 1.2 109.18 1.24 129.25 0a49.88 49.88 0 0 0 -.09-10.44c-19.89-1.22-107.28-1.15-129.1.09a65.41 65.41 0 0 0 -.05 10.36z" fill="#191919"/>
          </g> 
          <g> 
              <path d="m195.72 238.09c-3.5 2.24-138 1.79-141 0s-2.72-23.27 0-25.09 137.78-2.46 141 0 3.5 22.86 0 25.09z" fill="#e83a2a"/> </g> <g> <path d="m205.41 211.92c-3.5 2.24-138 1.79-141 0s-2.69-23.27 0-25.06 137.78-2.46 141 0 3.5 22.82 0 25.06z" fill="#191919"/> <path d="m142.85 221.43h-8.16c-17.77 0-34.7-.19-47.66-.44-23.88-.47-24.66-.95-26.77-2.22-4-2.43-5.75-7.44-6-17.28-.37-17.27 3.86-20.09 5.67-21.29 2.23-1.48 2.9-1.93 27-2.44 12.89-.27 30.29-.45 47.74-.49 18.4 0 35.39.06 47.86.31 24.49.48 25.37 1.14 27.71 2.93 5.23 4 5.94 14.12 5.62 21.92-.36 8.74-2.26 13.75-6.16 16.24-2.3 1.47-4.33 2.76-66.85 2.76zm-72.4-16.91c21.68 1.2 109.18 1.24 129.25 0a49.88 49.88 0 0 0 -.09-10.44c-19.89-1.22-107.28-1.15-129.1.09a65.41 65.41 0 0 0 -.05 10.36z" fill="#FFF"/> 
          </g> 
          <g> 
              <path d="m205.41 211.92c-3.5 2.24-138 1.79-141 0s-2.69-23.27 0-25.06 137.78-2.46 141 0 3.5 22.82 0 25.06z" fill="#FFF"/> 
          </g> 
          <g> 
              <path d="m195.45 186.63c-7 1-138 1.79-141 0s-2.69-23.27 0-25.06 137.78-2.46 141 0 4.11 24.43 0 25.06z" fill="#191919"/> <path d="m105.94 195.71c-51.94 0-53.38-.87-55.63-2.23-4-2.43-5.75-7.44-6-17.28-.31-17.26 3.9-20.08 5.69-21.28 2.23-1.48 2.9-1.93 27-2.44 12.89-.27 30.29-.45 47.74-.49 18.4 0 35.39.06 47.86.31 24.49.48 25.37 1.14 27.71 2.93 7.36 5.61 6.24 25.26 4.89 30.45-1.8 6.92-6.17 8.54-8.61 8.89-5.38.77-54.62 1.07-73.14 1.13zm-45.44-16.52c21.36.93 108.42.48 129.63-.25a50.78 50.78 0 0 0 -.4-10.07c-19.76-1.22-107.32-1.16-129.17.08a65.53 65.53 0 0 0 -.06 10.24z" fill="#191919"/> 
          </g> 
          <g>
               <path d="m195.45 186.63c-7 1-138 1.79-141 0s-2.69-23.27 0-25.06 137.78-2.46 141 0 4.11 24.43 0 25.06z" fill="#e83a2a"/> </g> <g> <path d="m200.55 89.36c0-40.28-30.58-74.12-72.94-72.94a74.66 74.66 0 0 0 -72.93 72.94c-1 39.79 30 73.25 72.94 72.94s72.14-32.43 72.93-72.94z" fill="#191919"/> <path d="m127 170.3c-22.31 0-42.83-8.38-57.85-23.65a78.52 78.52 0 0 1 -22.47-57.48 82.88 82.88 0 0 1 80.71-80.74 77.44 77.44 0 0 1 56.7 21.72c15.55 15.12 24.46 36.7 24.46 59.21v.16c-.91 46.48-34.92 80.48-80.87 80.78zm2.73-145.9h-1.89a66.88 66.88 0 0 0 -65.16 65.15 62.65 62.65 0 0 0 17.9 45.88c11.98 12.18 28.42 18.87 46.42 18.87h.53c18.53-.14 34.64-6.7 46.58-19 11.51-11.83 18-28.18 18.41-46 0-18.19-7.17-35.56-19.62-47.66a61.37 61.37 0 0 0 -43.17-17.24z" fill="#191919"/> <path d="m200.55 89.36c0-40.28-30.58-74.12-72.94-72.94a74.66 74.66 0 0 0 -72.93 72.94c-1 39.79 30 73.25 72.94 72.94s72.14-32.43 72.93-72.94z" fill="#e83a2a"/> <path d="m181.16 89.86c0-29.35-19.73-54-53.15-53.15s-53.15 23.8-53.15 53.15 15.3 52.34 53.14 53.14 53.16-23.79 53.16-53.14z" fill="#ffffff"/> </g> <g> <path d="m114.45 61.75c-4 2.52-2.79 54.16-.49 57.21s45.78-24.37 45.82-29.25-41.35-30.48-45.33-27.96z" fill="#e83a2a"/> 
          </g>
          </g>
          
      </svg>
      {% else %}
      <svg title="Please Select a Category" width="45px" height="45px" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg"><g id="SVGRepo_bgCarrier" stroke-width="0"></g><g id="SVGRepo_tracerCarrier" stroke-linecap="round" stroke-linejoin="round"></g><g id="SVGRepo_iconCarrier"> <path d="M20.5 9.03451C19.2743 5.52092 15.9315 3 12 3C7.02944 3 3 7.02944 3 12C3 16.9706 7.02944 21 12 21C15.5337 21 18.5918 18.9634 20.0645 16M12.0001 12L18.364 18.364M5.63599 5.63605L10 10M5.63605 18.364L18.364 5.63603" stroke="#e01b24" stroke-width="1.5" stroke-linecap="round"></path> </g></svg>
      {% endif %}

      
      {% endif %}
  </td>
  <td class="" style="background-color: #555">
      <a href="" hx-get="{% url "website:duplicate-entry" entry.id %}" hx-target="#meta-body" hx-swap="beforeend" class="text-white">
          <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor" class="bi bi-copy" viewBox="0 0 16 16">
              <path fill-rule="evenodd" d="M4 2a2 2 0 0 1 2-2h8a2 2 0 0 1 2 2v8a2 2 0 0 1-2 2H6a2 2 0 0 1-2-2zm2-1a1 1 0 0 0-1 1v8a1 1 0 0 0 1 1h8a1 1 0 0 0 1-1V2a1 1 0 0 0-1-1zM2 5a1 1 0 0 0-1 1v8a1 1 0 0 0 1 1h8a1 1 0 0 0 1-1v-1h1v1a2 2 0 0 1-2 2H2a2 2 0 0 1-2-2V6a2 2 0 0 1 2-2h1v1z"/>
            </svg>
      </a>
  </td>
</tr>