
<div  class="custom-form mb-lg-0 my-4 text-center" role="search" id="story" name="story">
    <div class="text-center text-white my-3 d-flex justify-content-around">

        <input type="url" placeholder="Video URL" required hx-target="#clip-meta" hx-swap="innerhtml" hx-post="{% url "website:get-clip-meta" %}" name="podcast-url" class=" text-center model3-url">

    </div>

    <div id="clip-meta">
        
    </div>
    
    <div class="w-50 mx-auto" id="recent-podcasts">
        <div class=" mt-4 py-2 rounded text-dark bg-white">
            Recent
        </div>
        <ul class="bg-white rounded text-center ps-2 bg-dark list-unstyled text-white py-3 ">
            {% for podcast in podcasts %}
                <li class="my-2"><a hx-get="{% url "website:get-clip-meta-with-id" podcast.id %}" hx-target="#clip-meta" hx-swap="innerhtml" class="text-white">{{podcast.title}}</a></li>
            {% endfor %}
            <li></li>
            <li></li>
            <li></li>
            <li></li>
        </ul>
    </div>


    
</div>  