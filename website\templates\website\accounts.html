{% extends "base.html" %}
{% block content %}
{% include "partials/nav.html" %}

    <div class="container hero-section text-white">

        <div class="container">
          <header class="d-flex flex-wrap justify-content-center py-3 mb-4 border-bottom">
            <a href="/" class="d-flex align-items-center mb-3 mb-md-0 me-md-auto text-dark text-decoration-none">
              <span class="fs-4 text-white">Accounts</span>
            </a>
      
            <a href="{% url "website:add-account-page" %}"><button class="btn btn-secondary">+ Add account</button></a>   
          </header>
        </div>
        <div class="row row-cols-1 row-cols-md-6 row-cols-sm-3 g-4">
            {% for account  in accounts  %}
            <div class="col account-{{account.id}}">
              <div class="card h-100 acc-card">
                <div>
                    <img src="{{ account.logo.url }}" class="card-img-top" alt="...">
                </div>
                <h6 class="card-title text-center">@{% if account.username %} {{account.username}}{% endif %}</h6>
   
                <div class="  w-75 mx-auto text-center">
                    <hr class="p-0 mb-1 m-0">
                    <strong class="border-ouline-secondary border-2 ">Category <br> <small> {{account.category}}</small></strong>
                    <hr class="p-0 m-0 mt-1">
                </div>
                <div class="card-footer border-0 text-center ">
                  
                  <a hx-get="{% url "website:edit-account" account.id %}" hx-target=".account-{{account.id}}" hx-swap="outerHTML" class="btn btn-outline-light  py-0 fw-bold  ">Edit</a>
                  <a hx-confirm="Delete {{account.username}} ?" hx-delete="{% url "website:delete-account" account.id %}" hx-target=".account-{{account.id}}" hx-swap="outerHTML" class="btn btn-outline-danger  py-0  mt-2 fw-bold  ">Delete</a>
                </div>
              </div>
            </div>
            {% endfor %}
          </div>
    </div>

{% endblock content %}