# Generated by Django 4.2.5 on 2023-10-19 16:55

from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        ("creator", "0002_alter_story_text_alter_story_title"),
    ]

    operations = [
        migrations.CreateModel(
            name="Category",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("name", models.CharField(max_length=100)),
            ],
        ),
        migrations.RenameField(
            model_name="story",
            old_name="created_at",
            new_name="created",
        ),
        migrations.RenameField(
            model_name="story",
            old_name="subtitles",
            new_name="script",
        ),
        migrations.RenameField(
            model_name="story",
            old_name="updated_at",
            new_name="updated",
        ),
        migrations.RemoveField(
            model_name="story",
            name="posted",
        ),
        migrations.RemoveField(
            model_name="story",
            name="text",
        ),
        migrations.AddField(
            model_name="story",
            name="video_url",
            field=models.URLField(blank=True),
        ),
        migrations.AlterField(
            model_name="story",
            name="title",
            field=models.CharField(max_length=200),
        ),
        migrations.CreateModel(
            name="UserProfile",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("username", models.CharField(max_length=50)),
                (
                    "logo",
                    models.ImageField(blank=True, null=True, upload_to="user_logos/"),
                ),
                ("created", models.DateTimeField(auto_now_add=True)),
                ("updated", models.DateTimeField(auto_now=True)),
                (
                    "category",
                    models.ForeignKey(
                        null=True,
                        on_delete=django.db.models.deletion.DO_NOTHING,
                        to="creator.category",
                    ),
                ),
            ],
        ),
        migrations.AddField(
            model_name="story",
            name="category",
            field=models.ForeignKey(
                blank=True,
                null=True,
                on_delete=django.db.models.deletion.SET_NULL,
                to="creator.category",
            ),
        ),
        migrations.AddField(
            model_name="story",
            name="user_profile",
            field=models.ForeignKey(
                blank=True,
                null=True,
                on_delete=django.db.models.deletion.SET_NULL,
                to="creator.userprofile",
            ),
        ),
    ]
