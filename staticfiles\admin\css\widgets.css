/* SELECTOR (FILTER INTERFACE) */

.selector {
    width: 800px;
    float: left;
    display: flex;
}

.selector select {
    width: 380px;
    height: 17.2em;
    flex: 1 0 auto;
}

.selector-available, .selector-chosen {
    width: 380px;
    text-align: center;
    margin-bottom: 5px;
    display: flex;
    flex-direction: column;
}

.selector-available h2, .selector-chosen h2 {
    border: 1px solid var(--border-color);
    border-radius: 4px 4px 0 0;
}

.selector-chosen .list-footer-display {
    border: 1px solid var(--border-color);
    border-top: none;
    border-radius: 0 0 4px 4px;
    margin: 0 0 10px;
    padding: 8px;
    text-align: center;
    background: var(--primary);
    color: var(--header-link-color);
    cursor: pointer;
}
.selector-chosen .list-footer-display__clear {
    color: var(--breadcrumbs-fg);
}

.selector-chosen h2 {
    background: var(--primary);
    color: var(--header-link-color);
}

.selector .selector-available h2 {
    background: var(--darkened-bg);
    color: var(--body-quiet-color);
}

.selector .selector-filter {
    border: 1px solid var(--border-color);
    border-width: 0 1px;
    padding: 8px;
    color: var(--body-quiet-color);
    font-size: 0.625rem;
    margin: 0;
    text-align: left;
}

.selector .selector-filter label,
.inline-group .aligned .selector .selector-filter label {
    float: left;
    margin: 7px 0 0;
    width: 18px;
    height: 18px;
    padding: 0;
    overflow: hidden;
    line-height: 1;
}

.selector .selector-available input,
.selector .selector-chosen input {
    width: 320px;
    margin-left: 8px;
}

.selector ul.selector-chooser {
    align-self: center;
    width: 22px;
    background-color: var(--selected-bg);
    border-radius: 10px;
    margin: 0 5px;
    padding: 0;
    transform: translateY(-17px);
}

.selector-chooser li {
    margin: 0;
    padding: 3px;
    list-style-type: none;
}

.selector select {
    padding: 0 10px;
    margin: 0 0 10px;
    border-radius: 0 0 4px 4px;
}
.selector .selector-chosen--with-filtered select {
    margin: 0;
    border-radius: 0;
    height: 14em;
}

.selector .selector-chosen:not(.selector-chosen--with-filtered) .list-footer-display {
    display: none;
}

.selector-add, .selector-remove {
    width: 16px;
    height: 16px;
    display: block;
    text-indent: -3000px;
    overflow: hidden;
    cursor: default;
    opacity: 0.55;
}

.active.selector-add, .active.selector-remove {
    opacity: 1;
}

.active.selector-add:hover, .active.selector-remove:hover {
    cursor: pointer;
}

.selector-add {
    background: url(../img/selector-icons.svg) 0 -96px no-repeat;
}

.active.selector-add:focus, .active.selector-add:hover {
    background-position: 0 -112px;
}

.selector-remove {
    background: url(../img/selector-icons.svg) 0 -64px no-repeat;
}

.active.selector-remove:focus, .active.selector-remove:hover {
    background-position: 0 -80px;
}

a.selector-chooseall, a.selector-clearall {
    display: inline-block;
    height: 16px;
    text-align: left;
    margin: 1px auto 3px;
    overflow: hidden;
    font-weight: bold;
    line-height: 16px;
    color: var(--body-quiet-color);
    text-decoration: none;
    opacity: 0.55;
}

a.active.selector-chooseall:focus, a.active.selector-clearall:focus,
a.active.selector-chooseall:hover, a.active.selector-clearall:hover {
    color: var(--link-fg);
}

a.active.selector-chooseall, a.active.selector-clearall {
    opacity: 1;
}

a.active.selector-chooseall:hover, a.active.selector-clearall:hover {
    cursor: pointer;
}

a.selector-chooseall {
    padding: 0 18px 0 0;
    background: url(../img/selector-icons.svg) right -160px no-repeat;
    cursor: default;
}

a.active.selector-chooseall:focus, a.active.selector-chooseall:hover {
    background-position: 100% -176px;
}

a.selector-clearall {
    padding: 0 0 0 18px;
    background: url(../img/selector-icons.svg) 0 -128px no-repeat;
    cursor: default;
}

a.active.selector-clearall:focus, a.active.selector-clearall:hover {
    background-position: 0 -144px;
}

/* STACKED SELECTORS */

.stacked {
    float: left;
    width: 490px;
    display: block;
}

.stacked select {
    width: 480px;
    height: 10.1em;
}

.stacked .selector-available, .stacked .selector-chosen {
    width: 480px;
}

.stacked .selector-available {
    margin-bottom: 0;
}

.stacked .selector-available input {
    width: 422px;
}

.stacked ul.selector-chooser {
    height: 22px;
    width: 50px;
    margin: 0 0 10px 40%;
    background-color: #eee;
    border-radius: 10px;
    transform: none;
}

.stacked .selector-chooser li {
    float: left;
    padding: 3px 3px 3px 5px;
}

.stacked .selector-chooseall, .stacked .selector-clearall {
    display: none;
}

.stacked .selector-add {
    background: url(../img/selector-icons.svg) 0 -32px no-repeat;
    cursor: default;
}

.stacked .active.selector-add {
    background-position: 0 -32px;
    cursor: pointer;
}

.stacked .active.selector-add:focus, .stacked .active.selector-add:hover {
    background-position: 0 -48px;
    cursor: pointer;
}

.stacked .selector-remove {
    background: url(../img/selector-icons.svg) 0 0 no-repeat;
    cursor: default;
}

.stacked .active.selector-remove {
    background-position: 0 0px;
    cursor: pointer;
}

.stacked .active.selector-remove:focus, .stacked .active.selector-remove:hover {
    background-position: 0 -16px;
    cursor: pointer;
}

.selector .help-icon {
    background: url(../img/icon-unknown.svg) 0 0 no-repeat;
    display: inline-block;
    vertical-align: middle;
    margin: -2px 0 0 2px;
    width: 13px;
    height: 13px;
}

.selector .selector-chosen .help-icon {
    background: url(../img/icon-unknown-alt.svg) 0 0 no-repeat;
}

.selector .search-label-icon {
    background: url(../img/search.svg) 0 0 no-repeat;
    display: inline-block;
    height: 1.125rem;
    width: 1.125rem;
}

/* DATE AND TIME */

p.datetime {
    line-height: 20px;
    margin: 0;
    padding: 0;
    color: var(--body-quiet-color);
    font-weight: bold;
}

.datetime span {
    white-space: nowrap;
    font-weight: normal;
    font-size: 0.6875rem;
    color: var(--body-quiet-color);
}

.datetime input, .form-row .datetime input.vDateField, .form-row .datetime input.vTimeField {
    margin-left: 5px;
    margin-bottom: 4px;
}

table p.datetime {
    font-size: 0.6875rem;
    margin-left: 0;
    padding-left: 0;
}

.datetimeshortcuts .clock-icon, .datetimeshortcuts .date-icon {
    position: relative;
    display: inline-block;
    vertical-align: middle;
    height: 16px;
    width: 16px;
    overflow: hidden;
}

.datetimeshortcuts .clock-icon {
    background: url(../img/icon-clock.svg) 0 0 no-repeat;
}

.datetimeshortcuts a:focus .clock-icon,
.datetimeshortcuts a:hover .clock-icon {
    background-position: 0 -16px;
}

.datetimeshortcuts .date-icon {
    background: url(../img/icon-calendar.svg) 0 0 no-repeat;
    top: -1px;
}

.datetimeshortcuts a:focus .date-icon,
.datetimeshortcuts a:hover .date-icon {
    background-position: 0 -16px;
}

.timezonewarning {
    font-size: 0.6875rem;
    color: var(--body-quiet-color);
}

/* URL */

p.url {
    line-height: 20px;
    margin: 0;
    padding: 0;
    color: var(--body-quiet-color);
    font-size: 0.6875rem;
    font-weight: bold;
}

.url a {
    font-weight: normal;
}

/* FILE UPLOADS */

p.file-upload {
    line-height: 20px;
    margin: 0;
    padding: 0;
    color: var(--body-quiet-color);
    font-size: 0.6875rem;
    font-weight: bold;
}

.file-upload a {
    font-weight: normal;
}

.file-upload .deletelink {
    margin-left: 5px;
}

span.clearable-file-input label {
    color: var(--body-fg);
    font-size: 0.6875rem;
    display: inline;
    float: none;
}

/* CALENDARS & CLOCKS */

.calendarbox, .clockbox {
    margin: 5px auto;
    font-size: 0.75rem;
    width: 19em;
    text-align: center;
    background: var(--body-bg);
    color: var(--body-fg);
    border: 1px solid var(--hairline-color);
    border-radius: 4px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.15);
    overflow: hidden;
    position: relative;
}

.clockbox {
    width: auto;
}

.calendar {
    margin: 0;
    padding: 0;
}

.calendar table {
    margin: 0;
    padding: 0;
    border-collapse: collapse;
    background: white;
    width: 100%;
}

.calendar caption, .calendarbox h2 {
    margin: 0;
    text-align: center;
    border-top: none;
    font-weight: 700;
    font-size: 0.75rem;
    color: #333;
    background: var(--accent);
}

.calendar th {
    padding: 8px 5px;
    background: var(--darkened-bg);
    border-bottom: 1px solid var(--border-color);
    font-weight: 400;
    font-size: 0.75rem;
    text-align: center;
    color: var(--body-quiet-color);
}

.calendar td {
    font-weight: 400;
    font-size: 0.75rem;
    text-align: center;
    padding: 0;
    border-top: 1px solid var(--hairline-color);
    border-bottom: none;
}

.calendar td.selected a {
    background: var(--primary);
    color: var(--button-fg);
}

.calendar td.nonday {
    background: var(--darkened-bg);
}

.calendar td.today a {
    font-weight: 700;
}

.calendar td a, .timelist a {
    display: block;
    font-weight: 400;
    padding: 6px;
    text-decoration: none;
    color: var(--body-quiet-color);
}

.calendar td a:focus, .timelist a:focus,
.calendar td a:hover, .timelist a:hover {
    background: var(--primary);
    color: white;
}

.calendar td a:active, .timelist a:active {
    background: var(--header-bg);
    color: white;
}

.calendarnav {
    font-size: 0.625rem;
    text-align: center;
    color: #ccc;
    margin: 0;
    padding: 1px 3px;
}

.calendarnav a:link, #calendarnav a:visited,
#calendarnav a:focus, #calendarnav a:hover {
    color: var(--body-quiet-color);
}

.calendar-shortcuts {
    background: var(--body-bg);
    color: var(--body-quiet-color);
    font-size: 0.6875rem;
    line-height: 0.6875rem;
    border-top: 1px solid var(--hairline-color);
    padding: 8px 0;
}

.calendarbox .calendarnav-previous, .calendarbox .calendarnav-next {
    display: block;
    position: absolute;
    top: 8px;
    width: 15px;
    height: 15px;
    text-indent: -9999px;
    padding: 0;
}

.calendarnav-previous {
    left: 10px;
    background: url(../img/calendar-icons.svg) 0 0 no-repeat;
}

.calendarbox .calendarnav-previous:focus,
.calendarbox .calendarnav-previous:hover {
    background-position: 0 -15px;
}

.calendarnav-next {
    right: 10px;
    background: url(../img/calendar-icons.svg) 0 -30px no-repeat;
}

.calendarbox .calendarnav-next:focus,
.calendarbox .calendarnav-next:hover {
    background-position: 0 -45px;
}

.calendar-cancel {
    margin: 0;
    padding: 4px 0;
    font-size: 0.75rem;
    background: #eee;
    border-top: 1px solid var(--border-color);
    color: var(--body-fg);
}

.calendar-cancel:focus, .calendar-cancel:hover {
    background: #ddd;
}

.calendar-cancel a {
    color: black;
    display: block;
}

ul.timelist, .timelist li {
    list-style-type: none;
    margin: 0;
    padding: 0;
}

.timelist a {
    padding: 2px;
}

/* EDIT INLINE */

.inline-deletelink {
    float: right;
    text-indent: -9999px;
    background: url(../img/inline-delete.svg) 0 0 no-repeat;
    width: 16px;
    height: 16px;
    border: 0px none;
}

.inline-deletelink:focus, .inline-deletelink:hover {
    cursor: pointer;
}

/* RELATED WIDGET WRAPPER */
.related-widget-wrapper {
    float: left;       /* display properly in form rows with multiple fields */
    overflow: hidden;  /* clear floated contents */
}

.related-widget-wrapper-link {
    opacity: 0.3;
}

.related-widget-wrapper-link:link {
    opacity: .8;
}

.related-widget-wrapper-link:link:focus,
.related-widget-wrapper-link:link:hover {
    opacity: 1;
}

select + .related-widget-wrapper-link,
.related-widget-wrapper-link + .related-widget-wrapper-link {
    margin-left: 7px;
}

/* GIS MAPS */
.dj_map {
    width: 600px;
    height: 400px;
}
