from django.contrib import admin
from .models import Podcast, PodcastEntry, Account

class PodcastAdmin(admin.ModelAdmin):
    list_display = ('title', 'category','source', 'source_location', 'created', 'updated')

class AccountAdmin(admin.ModelAdmin):
    list_display = ('username', 'category', 'logo', 'created', 'updated')
class PodcastEntryAdmin(admin.ModelAdmin):
    list_display = ('__str__','start_time', 'end_time', 'completed', 'locations', 'processing', 'created', 'updated')
admin.site.register(Podcast, PodcastAdmin)
admin.site.register(PodcastEntry, PodcastEntryAdmin)
admin.site.register(Account, AccountAdmin)