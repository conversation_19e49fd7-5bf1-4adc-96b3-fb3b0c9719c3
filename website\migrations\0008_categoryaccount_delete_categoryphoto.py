# Generated by Django 4.2.5 on 2023-11-18 21:52

from django.db import migrations, models
import django.db.models.deletion
import website.models


class Migration(migrations.Migration):

    dependencies = [
        ("creator", "0004_alter_category_options_alter_story_options_and_more"),
        ("website", "0007_categoryphoto"),
    ]

    operations = [
        migrations.CreateModel(
            name="CategoryAccount",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("username", models.CharField(blank=True, max_length=50, null=True)),
                (
                    "photo",
                    models.ImageField(
                        blank=True,
                        null=True,
                        upload_to=website.models.category_logo_path,
                    ),
                ),
                ("created", models.DateTimeField(auto_now_add=True)),
                ("updated", models.DateTimeField(auto_now=True)),
                (
                    "category",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        to="creator.category",
                    ),
                ),
            ],
        ),
        migrations.DeleteModel(
            name="CategoryPhoto",
        ),
    ]
