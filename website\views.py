from creator.utils import generate_story
from django.shortcuts import get_object_or_404
from django.shortcuts import render, redirect 
from django.urls import reverse
from django.http import HttpResponse, HttpRequest
from pytubefix import YouTube
from creator.models import Category, Story
from .models import Podcast, PodcastEntry, Account
from .tasks import process_entry
from .utils import count_seconds, get_youtube_captions

# Create your views here.
def home(request):
    # podcast_split_screen("https://www.youtube.com/watch?v=3KCDMyXDecc&list=RD3KCDMyXDecc&index=1&pp=8AUB"
    #                      )
    categories = Category.objects.all()
    podcasts = Podcast.objects.all().order_by('-id')[:5]
    context = {
        'categories': categories,
        'podcasts': podcasts
    }
    

    # print(load_logos_and_clips(10))
    return render(request, "website/home.html" ,context)

def accounts(request):
    accounts = Account.objects.all()
    context = {
        'accounts': accounts
    }
    return render(request, 'website/accounts.html', context)

def story(request):
    accounts = Account.objects.all()
    context = {
        'accounts': accounts
    }
    return render(request, 'website/story.html', context)

def  history(request):
    clips = PodcastEntry.objects.all().order_by('-id')[0:10]
    context = {
        'clips': clips
    }
    return render(request, 'website/history.html', context)

def add_account_page(request):
    categories = Category.objects.all()
    context = {
        'categories': categories
    }
    return render(request, 'website/add_account.html', context)

def add_account(request):
    username = request.POST.get('username').strip()
    category = request.POST.get('category')
    logo = request.FILES.get('logo')
    account, created = Account.objects.get_or_create(username=username, category=Category.objects.get(name=category))
    if created:
        if logo:
            account.logo = logo
            account.save()
    else:
        print("Account already exists.")
    return redirect('website:accounts')

def edit_account(request, id):
    account = get_object_or_404(Account, id=id)
    categories = Category.objects.all()
    context = {
        'categories': categories,
        'account': account
    }
    if request.method == 'GET':
        return render(request, 'partials/edit_account.html', context)
    
    elif request.method == 'POST':
        # Assuming 'category' field in request.POST contains the ID of the selected category
        category = request.POST.get('category')
        if category:
            category = get_object_or_404(Category, name=category)
            account.category = category
        
        # Update other fields as needed
        if 'username' in request.POST:
            account.username = request.POST['username']
        if 'logo' in request.FILES:
            account.logo = request.FILES.get('logo')
        account.save()
        return render(request,"partials/account.html", context)
 

def get_account(request, id):
    account = Account.objects.get(id=id)
    categories = Category.objects.all()
    context = {
        'categories': categories,
        'account' : account
    }
    return render(request, 'partials/account.html', context)

def delete_account(request, id):
    Account.objects.get(id=id).delete()
    return HttpResponse("")


def switch_to_model_1(request):
    return render(request, 'models/model_1.html')


def switch_to_model_2(request):
    categories = Category.objects.all()
    context = {
        'categories': categories
    }
    return render(request, 'models/model_2.html', context)


def switch_to_model_3(request):
    podcasts = Podcast.objects.all().order_by('-id')[:5]
    context = {
        'podcasts': podcasts[:5]
    }
    return render(request, 'models/model_3.html', context)

def get_clip_meta(request, id=None):
    categories = Category.objects.all()
    if request.method == 'POST':
        clip_url = request.POST.get('podcast-url').strip()
        print(clip_url)
        yt = YouTube(clip_url)
        print(yt)
        if yt.length is not None:
            length = str(yt.length // 60) + ":" + str(yt.length % 60)
        else:
            length = "Unknown"

        podcast, created = Podcast.objects.get_or_create(title=yt.title, source=clip_url, image_source=yt.thumbnail_url, duration=length)
        podcast.views = yt.views
        podcast.save()
        context = {
            "podcast": podcast,
            "length": length,
            "categories": categories,
        }
        return render(request, 'partials/clip_meta.html', context)
    elif request.method == 'GET' and id is not None:
        podcast = Podcast.objects.get(id=id)
        context = {
            "podcast": podcast,
            "length": podcast.duration,
            "categories": categories,
        }
    return render(request, 'partials/clip_meta.html', context)




def add_pod_entry(request, id):
    podcast = Podcast.objects.get(id=id)
    entry = PodcastEntry(podcast=podcast)
    entry.save()
    context = {
        "podcast": podcast,
        "entry": entry,
    }
    return render(request, 'partials/podcast_entry.html', context)


def create_story(request: HttpRequest):
    story = request.POST.get('story')
    title = request.POST.get('story-title')
    category = request.POST.get('category')
    category = Category.objects.get(name=category)
    obj = Story(title=title, script=story, category=category)
    obj.save()
    generate_story(story)
    print(title, category, story)
    return HttpResponse('story has been generated')

def generate_entry(request, id):

    entry = PodcastEntry.objects.get(id=id)

    entry.fetch_source_location()
    process_entry.delay(id)
    status_url = reverse('website:entry-process-status', args=[entry.id])

    # Use f-string to insert the URL into the SVG
    svg_content = f'''
    <svg id="status-container" 
         hx-get="{status_url}" 
         hx-trigger="every 5s" 
         hx-target="#status-container"
         hx-swap="outerHTML"
         fill="#e5a50a" 
         class="spinning" 
         version="1.1" 
         id="Layer_1" 
         xmlns="http://www.w3.org/2000/svg" 
         xmlns:xlink="http://www.w3.org/1999/xlink" 
         viewBox="0 0 32 32" 
         xml:space="preserve" 
         width="64px" 
         height="64px">
        <g id="SVGRepo_bgCarrier" stroke-width="0"/>
        <g id="SVGRepo_tracerCarrier" stroke-linecap="round" stroke-linejoin="round"/>
        <g id="SVGRepo_iconCarrier">
            <path id="process_1_" d="M17.38,23.36h-2.76c-0.199,0-0.36-0.161-0.36-0.36v-1.567l-0.869-0.363l-1.111,1.111 
                   c-0.067,0.067-0.159,0.105-0.254,0.105l0,0c-0.096,0-0.187-0.038-0.254-0.105L9.82,20.229c-0.141-0.141-0.141-0.369,0-0.51 
                   l1.108-1.107l-0.357-0.871H9c-0.199,0-0.36-0.161-0.36-0.36v-2.76c0-0.199,0.161-0.36,0.36-0.36h1.567l0.363-0.869L9.82,12.28 
                   c-0.14-0.141-0.14-0.368,0-0.509l1.951-1.952c0.135-0.136,0.374-0.136,0.509,0l1.108,1.109l0.872-0.357V9 
                   c0-0.199,0.161-0.36,0.36-0.36h2.76c0.199,0,0.36,0.161,0.36,0.36v1.567l0.868,0.363l1.11-1.111c0.135-0.135,0.375-0.136,0.51,0 
                   l1.952,1.952c0.067,0.067,0.105,0.159,0.105,0.254c0,0.096-0.038,0.187-0.105,0.254l-1.109,1.108l0.357,0.872H23 
                   c0.199,0,0.36,0.161,0.36,0.36v2.76c0,0.199-0.161,0.36-0.36,0.36h-1.567l-0.363,0.868l1.111,1.11 
                   c0.067,0.067,0.105,0.159,0.105,0.255s-0.038,0.188-0.105,0.255l-1.952,1.952c-0.067,0.067-0.159,0.105-0.255,0.105l0,0 
                   c-0.096,0-0.188-0.038-0.255-0.105l-1.107-1.109l-0.871,0.357V23C17.74,23.199,17.579,23.36,17.38,23.36z 
                   M14.98,22.64h2.04v-1.452c0-0.146,0.089-0.277,0.224-0.333l1.316-0.54c0.134-0.057,0.288-0.024,0.392,0.078l1.022,1.024l1.443-1.443l-1.028-1.026 
                   c-0.103-0.104-0.134-0.259-0.077-0.394l0.55-1.312c0.056-0.134,0.187-0.222,0.332-0.222h1.446v-2.04h-1.452 
                   c-0.146,0-0.277-0.088-0.333-0.223l-0.54-1.316c-0.055-0.134-0.024-0.289,0.078-0.391l1.024-1.023l-1.443-1.443l-1.026,1.027 
                   c-0.104,0.103-0.259,0.135-0.394,0.078l-1.312-0.549c-0.134-0.056-0.222-0.187-0.222-0.332V9.36h-2.04v1.452 
                   c0,0.146-0.088,0.278-0.223,0.333l-1.316,0.54c-0.135,0.054-0.288,0.024-0.391-0.079l-1.023-1.023l-1.442,1.443l1.027,1.028 
                   c0.103,0.103,0.134,0.259,0.077,0.394L11.14,14.76c-0.056,0.134-0.187,0.221-0.332,0.221H9.36v2.04h1.452 
                   c0.146,0,0.278,0.089,0.333,0.224l0.54,1.316c0.055,0.135,0.024,0.289-0.079,0.392l-1.022,1.022l1.442,1.443l1.028-1.028 
                   c0.104-0.104,0.259-0.133,0.394-0.077l1.312,0.55c0.134,0.056,0.221,0.187,0.221,0.332L14.98,22.64L14.98,22.64z 
                   M16,19.36c-1.853,0-3.36-1.508-3.36-3.36s1.507-3.36,3.36-3.36s3.36,1.507,3.36,3.36S17.853,19.36,16,19.36z 
                   M16,13.36c-1.456,0-2.64,1.185-2.64,2.64c0,1.456,1.185,2.64,2.64,2.64c1.456,0,2.64-1.184,2.64-2.64S17.456,13.36,16,13.36z 
                   M16,31.36c-4.529,0-8.716-1.933-11.64-5.338V30H3.64v-5.36H9v0.721H4.743C7.536,28.725,11.598,30.64,16,30.64 
                   c8.072,0,14.64-6.567,14.64-14.64h0.721C31.36,24.47,24.47,31.36,16,31.36z 
                   M1.36,16H0.64C0.64,7.53,7.53,0.64,16,0.64c4.529,0,8.716,1.933,11.64,5.337V2h0.721v5.36H23V6.64h4.257 
                   C24.464,3.275,20.401,1.36,16,1.36C7.927,1.36,1.36,7.927,1.36,16z"/>
            <rect id="_Transparent_Rectangle" style="fill:none;" width="32" height="32"/>
        </g>
    </svg>
    '''

    return HttpResponse(svg_content)




def modify_pod_entry(request, id):
    block = PodcastEntry.objects.get(id=id)
    target = list(request.POST.items())[0][0]
    field_value = list(request.POST.items())[0][1]
    if target == "start":
        number_of_seconds = count_seconds(field_value)
        block.start_time = number_of_seconds
    elif target == "end":
        number_of_seconds = count_seconds(field_value)
        block.end_time = number_of_seconds
    elif target == "transcripe":
        block.transcripe = not block.transcripe
        block.save()
        if block.transcripe:
            return HttpResponse('')

    else:
        pass
    block.save()
    return HttpResponse('')


def duplicate_entry(request, id):
    entry = PodcastEntry.objects.get(id=id)
    dup = PodcastEntry(podcast=entry.podcast, start_time=entry.start_time, end_time=entry.end_time, transcripe=entry.transcripe, completed=False, processing=False)
    dup.save()
    context = {
        "entry": dup
    }
    return render(request, "partials/duplicate_entry.html", context)

def check_entry_processing_status(request, id):
    entry = PodcastEntry.objects.get(id=id)
    if entry.completed:
        html = f'<a href="{entry.get_first_location}" target="_blank" class="btn btn-outline-secondary rounded-pill border-2 text-white fw-bolder mx-auto">View</a>'
    elif entry.processing:
        return HttpResponse('')
    else:
        html = '<span>Status Unknown</span>'
    return HttpResponse(html)


def delete_podcast_entry(request, id):
    block = PodcastEntry.objects.get(id=id)
    block.delete()
    return HttpResponse('')

def delete_podcast(request,id):
    podcast = Podcast.objects.get(id=id)
    podcast.delete()
    return HttpResponse('')

def set_podcast_category(request, id):
    podcast = Podcast.objects.get(id=id)
    category = request.POST.get("category")
    category = Category.objects.get(name=category)
    podcast.category = category
    podcast.save()
    return HttpResponse('')