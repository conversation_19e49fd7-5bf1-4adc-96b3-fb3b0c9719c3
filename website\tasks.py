"""
Celery tasks for video processing and background operations.
"""

import logging
from celery import shared_task
from creator.utils import podcast_split_screen
from .models import PodcastEntry

logger = logging.getLogger(__name__)

@shared_task(bind=True)
def process_entry(self, entry_id):
    """
    Process a podcast entry by creating a split-screen video.

    Args:
        entry_id (int): ID of the PodcastEntry to process

    Returns:
        str: Path to the processed video file
    """
    try:
        logger.info(f"Starting processing for entry {entry_id}")
        source = podcast_split_screen(entry_id)

        # Mark entry as completed
        entry = PodcastEntry.objects.get(id=entry_id)
        entry.mark_as_completed()

        logger.info(f"Successfully processed entry {entry_id}")
        return source

    except Exception as e:
        logger.error(f"Error processing entry {entry_id}: {str(e)}")
        raise

@shared_task(bind=True)
def cleanup_failed_entries(self):
    """
    Clean up entries that have been stuck in processing state.

    Returns:
        int: Number of entries cleaned up
    """
    try:
        # Find entries stuck in processing for more than 1 hour
        from django.utils import timezone
        from datetime import timedelta

        cutoff_time = timezone.now() - timedelta(hours=1)
        stuck_entries = PodcastEntry.objects.filter(
            processing=True,
            updated__lt=cutoff_time
        )

        count = stuck_entries.count()
        stuck_entries.update(processing=False)

        logger.info(f"Cleaned up {count} stuck entries")
        return count

    except Exception as e:
        logger.error(f"Error cleaning up failed entries: {str(e)}")
        raise