<div class="col account-{{account.id}} ">
    <div class="card h-100 mx-auto acc-card">
      
      <form enctype="multipart/form-data" hx-post="{% url "website:edit-account" account.id %}" hx-target=".account-{{account.id}}" hx-swap="outerHTML" method="post" class="text-center text-white  rounded ">
        
        {% csrf_token %}
        
        <div class="">
          
          <label for="formFile" class="form-label">
            <img class="img-fluid" src="{{ account.logo.url }}" alt="logo">
          </label>
          <input class="form-control" hidden type="file" id="formFile" name="logo">
        </div>
        <div class="">
          <label class="" for="inlineFormInputGroupUsername">Username</label>
          <div class="input-group">
            <div class="input-group-text bg-dark text-white fw-bold">@</div>
            <input type="text" class="form-control bg-dark text-white fw-bold" required {% if account.username %}value="{{account.username}}"{% endif %} name="username" id="inlineFormInputGroupUsername" placeholder="Username">
          </div>
        </div>
      
        <div class="pt-2">
          <strong class="border-ouline-secondary border-2">Category <br> 
            <select class="form-select  mx-auto bg-dark text-white fw-bold" id="inlineFormSelectPref" name="category" hx-post="{% url "website:edit-account" account.id %}" hx-swap="none">
              {% if account.category %}
                <option selected disabled value="{{account.category.name}}">{{account.category.name}}</option> 
              {% else %}
                <option disabled></option>
              {% endif %}
              {% for category in categories %}
                  <option value="{{category.name}}">{{category.name}}</option>
              {% endfor %}
            </select>
          </strong>
        </div>
      

        
        <div class="card-footer border-0 text-center d-flex justify-content-evenly">
          
          <button type="submit" class="btn btn-outline-success border-2 py-0  fw-bold  ">Save</button>
          
        </div>
      </form>
    </div>
  </div>
