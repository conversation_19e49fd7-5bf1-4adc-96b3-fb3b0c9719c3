<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Wireless Earbuds - Premium Sound Experience</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }
        
        body {
            background-color: #f8f9fa;
            color: #333;
            line-height: 1.6;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }
        
        header {
            background: linear-gradient(to right, #4a00e0, #8e2de2);
            color: white;
            padding: 15px 0;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
        }
        
        .header-content {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 0 20px;
        }
        
        .logo {
            font-size: 24px;
            font-weight: bold;
            display: flex;
            align-items: center;
        }
        
        .logo i {
            margin-right: 10px;
        }
        
        nav ul {
            display: flex;
            list-style: none;
        }
        
        nav ul li {
            margin-left: 20px;
        }
        
        nav ul li a {
            color: white;
            text-decoration: none;
            font-weight: 500;
            transition: opacity 0.3s;
        }
        
        nav ul li a:hover {
            opacity: 0.8;
        }
        
        .cart-icon {
            position: relative;
            font-size: 20px;
        }
        
        .cart-count {
            position: absolute;
            top: -10px;
            right: -10px;
            background-color: #ff4757;
            color: white;
            border-radius: 50%;
            width: 20px;
            height: 20px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 12px;
        }
        
        .product-container {
            display: flex;
            flex-wrap: wrap;
            gap: 30px;
            margin-top: 40px;
            background: white;
            border-radius: 12px;
            overflow: hidden;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.05);
        }
        
        .product-gallery {
            flex: 1;
            min-width: 300px;
            padding: 20px;
        }
        
        .main-image {
            width: 100%;
            border-radius: 8px;
            overflow: hidden;
            box-shadow: 0 4px 10px rgba(0, 0, 0, 0.1);
        }
        
        .main-image img {
            width: 100%;
            display: block;
            transition: transform 0.5s;
        }
        
        .main-image img:hover {
            transform: scale(1.02);
        }
        
        .thumbnail-container {
            display: flex;
            gap: 10px;
            margin-top: 15px;
        }
        
        .thumbnail {
            width: 80px;
            height: 80px;
            border-radius: 8px;
            overflow: hidden;
            cursor: pointer;
            border: 2px solid transparent;
            transition: all 0.3s;
        }
        
        .thumbnail:hover {
            border-color: #4a00e0;
        }
        
        .thumbnail img {
            width: 100%;
            height: 100%;
            object-fit: cover;
        }
        
        .product-info {
            flex: 1;
            min-width: 300px;
            padding: 20px;
        }
        
        .product-title {
            font-size: 28px;
            margin-bottom: 10px;
            color: #2c3e50;
        }
        
        .product-rating {
            display: flex;
            align-items: center;
            margin-bottom: 15px;
        }
        
        .stars {
            color: #ffc107;
            margin-right: 10px;
        }
        
        .rating-value {
            color: #6c757d;
            font-weight: 500;
        }
        
        .product-price {
            display: flex;
            align-items: center;
            margin-bottom: 20px;
        }
        
        .current-price {
            font-size: 32px;
            font-weight: bold;
            color: #4a00e0;
        }
        
        .original-price {
            text-decoration: line-through;
            color: #6c757d;
            margin-left: 15px;
            font-size: 20px;
        }
        
        .discount-badge {
            background-color: #ff4757;
            color: white;
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 14px;
            margin-left: 15px;
        }
        
        .product-description {
            margin-bottom: 25px;
            line-height: 1.8;
            color: #495057;
        }
        
        .product-features {
            margin-bottom: 25px;
        }
        
        .feature-list {
            list-style: none;
        }
        
        .feature-list li {
            margin-bottom: 10px;
            display: flex;
            align-items: center;
        }
        
        .feature-list li i {
            color: #4a00e0;
            margin-right: 10px;
        }
        
        .variations {
            margin-bottom: 25px;
        }
        
        .variation-title {
            font-weight: 600;
            margin-bottom: 10px;
            color: #2c3e50;
        }
        
        .color-options {
            display: flex;
            gap: 10px;
            margin-bottom: 15px;
        }
        
        .color-option {
            width: 30px;
            height: 30px;
            border-radius: 50%;
            cursor: pointer;
            border: 2px solid transparent;
            transition: all 0.3s;
        }
        
        .color-option.selected {
            border-color: #4a00e0;
            transform: scale(1.1);
        }
        
        .color-black {
            background-color: #2c3e50;
        }
        
        .color-white {
            background-color: #f8f9fa;
            border: 1px solid #dee2e6;
        }
        
        .color-blue {
            background-color: #007bff;
        }
        
        .quantity-selector {
            display: flex;
            align-items: center;
            margin-bottom: 25px;
        }
        
        .quantity-title {
            margin-right: 15px;
            font-weight: 600;
            color: #2c3e50;
        }
        
        .quantity-controls {
            display: flex;
            align-items: center;
            border: 1px solid #dee2e6;
            border-radius: 4px;
            overflow: hidden;
        }
        
        .quantity-btn {
            background: #f8f9fa;
            border: none;
            width: 40px;
            height: 40px;
            font-size: 18px;
            cursor: pointer;
            transition: background 0.3s;
        }
        
        .quantity-btn:hover {
            background: #e9ecef;
        }
        
        .quantity-input {
            width: 50px;
            height: 40px;
            text-align: center;
            border: none;
            border-left: 1px solid #dee2e6;
            border-right: 1px solid #dee2e6;
            font-size: 16px;
        }
        
        .add-to-cart-btn {
            display: inline-block;
            background: linear-gradient(to right, #4a00e0, #8e2de2);
            color: white;
            padding: 15px 30px;
            border: none;
            border-radius: 8px;
            font-size: 18px;
            font-weight: 600;
            cursor: pointer;
            transition: transform 0.3s, box-shadow 0.3s;
            margin-right: 15px;
        }
        
        .add-to-cart-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 12px rgba(74, 0, 224, 0.2);
        }
        
        .buy-now-btn {
            display: inline-block;
            background: #ff4757;
            color: white;
            padding: 15px 30px;
            border: none;
            border-radius: 8px;
            font-size: 18px;
            font-weight: 600;
            cursor: pointer;
            transition: transform 0.3s, box-shadow 0.3s;
        }
        
        .buy-now-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 12px rgba(255, 71, 87, 0.2);
        }
        
        .shipping-info {
            background-color: #e9f7fe;
            padding: 15px;
            border-radius: 8px;
            margin-top: 20px;
            display: flex;
            align-items: center;
        }
        
        .shipping-info i {
            color: #007bff;
            font-size: 24px;
            margin-right: 15px;
        }
        
        .shipping-text {
            color: #495057;
        }
        
        .shipping-text strong {
            color: #007bff;
        }
        
        .product-specs {
            margin-top: 40px;
            background: white;
            border-radius: 12px;
            padding: 25px;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.05);
        }
        
        .specs-title {
            font-size: 24px;
            margin-bottom: 20px;
            color: #2c3e50;
            padding-bottom: 10px;
            border-bottom: 2px solid #f1f3f5;
        }
        
        .specs-table {
            width: 100%;
            border-collapse: collapse;
        }
        
        .specs-table tr {
            border-bottom: 1px solid #f1f3f5;
        }
        
        .specs-table td {
            padding: 15px 10px;
        }
        
        .specs-table td:first-child {
            font-weight: 600;
            color: #2c3e50;
            width: 30%;
        }
        
        footer {
            background-color: #2c3e50;
            color: white;
            padding: 40px 0;
            margin-top: 60px;
        }
        
        .footer-content {
            display: flex;
            flex-wrap: wrap;
            justify-content: space-between;
            gap: 30px;
        }
        
        .footer-section {
            flex: 1;
            min-width: 250px;
        }
        
        .footer-title {
            font-size: 18px;
            margin-bottom: 20px;
            position: relative;
            padding-bottom: 10px;
        }
        
        .footer-title::after {
            content: '';
            position: absolute;
            left: 0;
            bottom: 0;
            width: 50px;
            height: 2px;
            background: #4a00e0;
        }
        
        .footer-links {
            list-style: none;
        }
        
        .footer-links li {
            margin-bottom: 10px;
        }
        
        .footer-links a {
            color: #ddd;
            text-decoration: none;
            transition: color 0.3s;
        }
        
        .footer-links a:hover {
            color: #8e2de2;
        }
        
        .copyright {
            text-align: center;
            padding-top: 20px;
            margin-top: 20px;
            border-top: 1px solid #3e546c;
            color: #ddd;
        }
        
        @media (max-width: 768px) {
            .header-content {
                flex-direction: column;
                text-align: center;
            }
            
            nav ul {
                margin-top: 15px;
                justify-content: center;
            }
            
            .product-container {
                flex-direction: column;
            }
            
            .add-to-cart-btn, .buy-now-btn {
                width: 100%;
                margin-bottom: 10px;
                margin-right: 0;
            }
            
            .button-container {
                display: flex;
                flex-direction: column;
            }
        }
    </style>
</head>
<body>
    <header>
        <div class="header-content">
            <div class="logo">
                <i class="fas fa-headphones"></i>
                <span>AudioTech</span>
            </div>
            <nav>
                <ul>
                    <li><a href="#">Home</a></li>
                    <li><a href="#">Products</a></li>
                    <li><a href="#">About</a></li>
                    <li><a href="#">Contact</a></li>
                    <li>
                        <a href="#" class="cart-icon">
                            <i class="fas fa-shopping-cart"></i>
                            <span class="cart-count">0</span>
                        </a>
                    </li>
                </ul>
            </nav>
        </div>
    </header>

    <div class="container">
        <div class="product-container">
            <div class="product-gallery">
                <div class="main-image">
                    <img src="https://images.unsplash.com/photo-1606220588913-b3aacb4d2f46?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1000&q=80" alt="Wireless Earbuds" id="main-img">
                </div>
                <div class="thumbnail-container">
                    <div class="thumbnail" onclick="changeImage('https://images.unsplash.com/photo-1606220588913-b3aacb4d2f46?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1000&q=80')">
                        <img src="https://images.unsplash.com/photo-1606220588913-b3aacb4d2f46?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=200&q=80" alt="Earbuds Image 1">
                    </div>
                    <div class="thumbnail" onclick="changeImage('https://images.unsplash.com/photo-1590658268037-6bf12165a8df?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1000&q=80')">
                        <img src="https://images.unsplash.com/photo-1590658268037-6bf12165a8df?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=200&q=80" alt="Earbuds Image 2">
                    </div>
                    <div class="thumbnail" onclick="changeImage('https://images.unsplash.com/photo-1613040809024-b4ef7ba99bc3?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1000&q=80')">
                        <img src="https://images.unsplash.com/photo-1613040809024-b4ef7ba99bc3?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=200&q=80" alt="Earbuds Image 3">
                    </div>
                    <div class="thumbnail" onclick="changeImage('https://images.unsplash.com/photo-1572569511254-d8f925fe2cbb?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1000&q=80')">
                        <img src="https://images.unsplash.com/photo-1572569511254-d8f925fe2cbb?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=200&q=80" alt="Earbuds Image 4">
                    </div>
                </div>
            </div>

            <div class="product-info">
                <h1 class="product-title">Wireless Bluetooth Earbuds with Noise Cancellation</h1>
                <div class="product-rating">
                    <div class="stars">
                        <i class="fas fa-star"></i>
                        <i class="fas fa-star"></i>
                        <i class="fas fa-star"></i>
                        <i class="fas fa-star"></i>
                        <i class="fas fa-star-half-alt"></i>
                    </div>
                    <span class="rating-value">4.5 (1,248 reviews)</span>
                </div>
                
                <div class="product-price">
                    <span class="current-price">$59.99</span>
                    <span class="original-price">$99.99</span>
                    <span class="discount-badge">40% OFF</span>
                </div>
                
                <p class="product-description">
                    Experience premium sound quality with our latest wireless earbuds. Featuring active noise cancellation, 24-hour battery life, and water resistance, these earbuds are perfect for all your daily activities.
                </p>
                
                <div class="product-features">
                    <ul class="feature-list">
                        <li><i class="fas fa-check-circle"></i> Active Noise Cancellation</li>
                        <li><i class="fas fa-check-circle"></i> 24-hour battery life with charging case</li>
                        <li><i class="fas fa-check-circle"></i> IPX5 water resistance</li>
                        <li><i class="fas fa-check-circle"></i> Bluetooth 5.2 with 30ft range</li>
                        <li><i class="fas fa-check-circle"></i> Touch controls and voice assistant</li>
                    </ul>
                </div>
                
                <div class="variations">
                    <div class="variation-title">Color:</div>
                    <div class="color-options">
                        <div class="color-option color-black selected" title="Black" onclick="selectColor(this)"></div>
                        <div class="color-option color-white" title="White" onclick="selectColor(this)"></div>
                        <div class="color-option color-blue" title="Blue" onclick="selectColor(this)"></div>
                    </div>
                </div>
                
                <div class="quantity-selector">
                    <span class="quantity-title">Quantity:</span>
                    <div class="quantity-controls">
                        <button class="quantity-btn" onclick="decreaseQuantity()">-</button>
                        <input type="number" class="quantity-input" id="quantity" value="1" min="1" max="10">
                        <button class="quantity-btn" onclick="increaseQuantity()">+</button>
                    </div>
                </div>
                
                <div class="button-container">
                    <button class="add-to-cart-btn" onclick="addToCart()">
                        <i class="fas fa-shopping-cart"></i> Add to Cart
                    </button>
                    <button class="buy-now-btn" onclick="buyNow()">
                        <i class="fas fa-bolt"></i> Buy Now
                    </button>
                </div>
                
                <div class="shipping-info">
                    <i class="fas fa-truck"></i>
                    <div class="shipping-text">
                        <strong>Free Shipping</strong> on orders over $50. Expected delivery in 3-5 business days.
                    </div>
                </div>
            </div>
        </div>
        
        <div class="product-specs">
            <h2 class="specs-title">Product Specifications</h2>
            <table class="specs-table">
                <tr>
                    <td>Brand</td>
                    <td>AudioTech</td>
                </tr>
                <tr>
                    <td>Model</td>
                    <td>SoundBuds Pro 2023</td>
                </tr>
                <tr>
                    <td>Connectivity</td>
                    <td>Bluetooth 5.2</td>
                </tr>
                <tr>
                    <td>Battery Life</td>
                    <td>8 hours (earbuds), 24 hours (with case)</td>
                </tr>
                <tr>
                    <td>Charging Time</td>
                    <td>1.5 hours (earbuds), 2 hours (case)</td>
                </tr>
                <tr>
                    <td>Water Resistance</td>
                    <td>IPX5 (sweat and water resistant)</td>
                </tr>
                <tr>
                    <td>Weight</td>
                    <td>5g per earbud, 45g charging case</td>
                </tr>
                <tr>
                    <td>Warranty</td>
                    <td>1 year limited warranty</td>
                </tr>
            </table>
        </div>
    </div>

    <footer>
        <div class="container">
            <div class="footer-content">
                <div class="footer-section">
                    <h3 class="footer-title">About Us</h3>
                    <p>AudioTech is dedicated to providing high-quality audio products at affordable prices. We believe everyone deserves great sound.</p>
                </div>
                
                <div class="footer-section">
                    <h3 class="footer-title">Quick Links</h3>
                    <ul class="footer-links">
                        <li><a href="#">Home</a></li>
                        <li><a href="#">Products</a></li>
                        <li><a href="#">About Us</a></li>
                        <li><a href="#">Contact</a></li>
                        <li><a href="#">FAQ</a></li>
                    </ul>
                </div>
                
                <div class="footer-section">
                    <h3 class="footer-title">Contact Info</h3>
                    <ul class="footer-links">
                        <li><i class="fas fa-map-marker-alt"></i> 123 Audio Street, Sound City</li>
                        <li><i class="fas fa-phone"></i> +****************</li>
                        <li><i class="fas fa-envelope"></i> <EMAIL></li>
                    </ul>
                </div>
            </div>
            
            <div class="copyright">
                <p>&copy; 2023 AudioTech. All rights reserved.</p>
            </div>
        </div>
    </footer>

    <script>
        function changeImage(src) {
            document.getElementById('main-img').src = src;
            
            // Update active thumbnail
            const thumbnails = document.querySelectorAll('.thumbnail');
            thumbnails.forEach(thumb => thumb.classList.remove('active'));
            event.currentTarget.classList.add('active');
        }
        
        function selectColor(element) {
            const colorOptions = document.querySelectorAll('.color-option');
            colorOptions.forEach(option => option.classList.remove('selected'));
            element.classList.add('selected');
        }
        
        function increaseQuantity() {
            const quantityInput = document.getElementById('quantity');
            if (parseInt(quantityInput.value) < 10) {
                quantityInput.value = parseInt(quantityInput.value) + 1;
            }
        }
        
        function decreaseQuantity() {
            const quantityInput = document.getElementById('quantity');
            if (parseInt(quantityInput.value) > 1) {
                quantityInput.value = parseInt(quantityInput.value) - 1;
            }
        }
        
        function addToCart() {
            const quantity = document.getElementById('quantity').value;
            const cartCount = document.querySelector('.cart-count');
            cartCount.textContent = parseInt(cartCount.textContent) + parseInt(quantity);
            
            alert(`Added ${quantity} item(s) to your cart!`);
        }
        
        function buyNow() {
            addToCart();
            alert('Proceeding to checkout...');
            // In a real implementation, this would redirect to a checkout page
        }
    </script>
</body>
</html>