import os
from django.db import models
from django.conf import settings 
import json
from pytubefix import YouTube 

from creator.models import Category


media_root = settings.MEDIA_ROOT 

def category_logo_path(instance, filename):
    return f'creator/category/{instance.category.name}/logos/{filename}'

class Account(models.Model):
    username = models.CharField(max_length=50, blank=True, null=True, unique=True)
    category = models.ForeignKey(Category, on_delete=models.CASCADE)
    logo = models.ImageField(upload_to=category_logo_path, null=True, blank=True)
    created = models.DateTimeField(auto_now_add=True)
    updated = models.DateTimeField(auto_now=True)


    
class Podcast(models.Model):

    class Meta:
        verbose_name = "Podcast"
        verbose_name_plural = "Podcasts"


    source = models.CharField(max_length=255, blank=False, null=False)
    source_location = models.CharField(max_length=255, blank=True, null=True)

    title = models.CharField(max_length=200, blank=True, null=True)
    image_source = models.CharField(max_length=255, blank=True, null=True)
    description = models.TextField(blank=True, null=True)
    views = models.BigIntegerField(blank=True, null=True)
    duration = models.CharField(max_length=250, blank=True, null=True)

    category = models.ForeignKey(Category, on_delete=models.DO_NOTHING, blank=True, null=True)

    created = models.DateTimeField(auto_now_add=True)
    updated = models.DateTimeField(auto_now=True)
    
    def __str__(self):
        return self.title

class PodcastEntry(models.Model):
    class Meta:
        verbose_name = "Entry"
        verbose_name_plural = "Entries"

    podcast = models.ForeignKey(Podcast, on_delete=models.DO_NOTHING, blank=True, null=True)
    start_time = models.IntegerField(blank=True, null=True)
    end_time = models.IntegerField(blank=True, null=True)
    transcripe = models.BooleanField(default=False, blank=True, null=True)
    locations  = models.TextField(blank=True, null=True)
    completed = models.BooleanField(default=False)
    processing = models.BooleanField(default=False)
    created = models.DateTimeField(auto_now_add=True)
    updated = models.DateTimeField(auto_now=True)

    def __str__(self):
        return f"Entry {self.id}"
    
    def format_duration(self, seconds):
        hours = seconds // 3600
        minutes = (seconds % 3600) // 60
        seconds = seconds % 60

        if hours > 0:
            return f"{hours}:{minutes:02d}:{seconds:02d}"
        else:
            return f"{minutes}:{seconds:02d}"

    def formatted_start_time(self):
        return self.format_duration(self.start_time)

    def formatted_end_time(self):
        return self.format_duration(self.end_time)

    def mark_as_completed(self):
        self.completed = True
        self.processing = False
        self.save()

    def mark_as_processing(self):
        self.processing = True
        self.save()

    def update_entry_times(self, start_time, end_time):
        self.start_time = start_time
        self.end_time = end_time
        self.save()

    def update_locations(self, locations):
        self.locations = json.dumps(locations)
        self.save()

    @property
    def get_locations(self):
        return json.loads(self.locations)

    @property
    def get_first_location(self):
        return json.loads(self.locations)[0]

    def fetch_source_location(self):
        if not self.podcast.source_location:
            yt = YouTube(self.podcast.source)
        
            yt_stream = yt.streams.get_highest_resolution()

            category_dir = os.path.join(media_root, 'creator', 'category', self.podcast.category.name)
            download_path = os.path.join(category_dir, 'videos')

            video_path = yt_stream.download(output_path=download_path)
            self.podcast.source_location = video_path
            self.podcast.save()

    

    @classmethod
    def get_entries_for_podcast(cls, podcast_id):
        """Get all entries for a specific podcast."""
        return cls.objects.filter(podcast_id=podcast_id)

    @classmethod
    def get_completed_entries_for_podcast(cls, podcast_id):
        """Get all completed entries for a specific podcast."""
        return cls.objects.filter(podcast_id=podcast_id, completed=True)

    @classmethod
    def get_entries(cls):
        return cls.objects.all()

    @classmethod
    def get_incompleted_entries(cls):
        # Get a queryset of PodcastEntry objects that are not completed
        return cls.objects.filter(completed=False, start_time__isnull=False, end_time__isnull=False)
