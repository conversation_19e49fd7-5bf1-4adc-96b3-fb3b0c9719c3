body {
    /* background: rgb(36,0,0); */
    /* background: linear-gradient(90deg, rgba(36,0,0,1) 0%, rgba(79,1,16,1) 35%, rgba(255,0,37,1) 100%); */
    background-color: rgb(31, 28, 28);
}

#story-form textarea{
    
    border-radius: 20px 20px 0 0;
}

#story .story-btn{
    border-radius: 0 0 20px 20px !important;
}

#waiting {
    color: red;
}

ul {
    list-style-type: none !important;
}
.nav-link {
    color: #fff;
}

.navbar-brand img {
    max-width: 130px;
}

.nav-link.active{
    background-color: #dc3545 !important;
}

.htmx-indicator{
    opacity:0;
    transition: opacity 500ms ease-in;
}
.htmx-request .htmx-indicator{
    opacity:1
}
.htmx-request.htmx-indicator{
    opacity:1
}


.nav-link:hover {
    color: gray;
}

.nav-link:focus, .nav-link:focus-visible {
    color: gray;
    box-shadow: 0 0 0 .25rem rgba(253, 36, 13, 0.25);
}


button.trans-focus:focus {
    background-color: transparent;
}

.bg-trans{
    background: linear-gradient(90deg, rgba(36,0,0,1) 0%, rgba(79,1,16,1) 35%, rgba(255,0,37,1) 100%);
    
}
.bg-gray {
    background-color: gray;
}
.card h5 {
    background-color: inherit;
}

.acc-card {
    background: inherit;
    /* border: rgba(180, 178, 178, 0.637) 3px solid; */
    border: 0;
    color: white !important;
}

.acc-card input::placeholder {
    color: white;
}



#clip-meta{
    display: flex;
}
#clip-meta img{
    width: 85%;
    border: #dbdbdb 4px solid;
    border-radius: 4px;

}

#clip-meta  p{
    text-align: center;
}


#clip-meta a {
    text-decoration: none !important;
}

span.meta {
    color: skyblue;
}

#recent-podcasts a {
    text-decoration: none !important;
    margin: 1em 0;
}

#recent-podcasts a:hover {
    text-decoration: underline !important;
    cursor: pointer;
}

.pod-entries {
    width: 100%;
    border-radius: 9px;
    
}

.pod-entries .entry {
    border: red 2px solid;
    border-radius: 7px;
    margin-bottom: 1em;
}

.pod-entries .row {
    padding: .5em;
    margin-top: 0;
}

.pod-entries h5{
    color:skyblue;
}

#meta-table {
    background:transparent;
    border: 0;

}


#entries-table table{
    margin-bottom: 0;
    
}

#entries-table th,
#entries-table td {
    background: rgba(71, 69, 69, 0.253);
    color: white;
}

.card-body {
    padding: 0;
}

.bg-dark {
    background-color: #5e5e5e57 !important;
}

.model3-url {
    width: 60%;
    padding: 1em 0;
    border-radius: 20px;
    background-color: #5e5e5e57;
    color:white;
    font-weight: bold;
}

#meta-body {
    vertical-align: middle;
}

@keyframes spin {
    from {
        transform: rotate(0deg);
    }
    to {
        transform: rotate(360deg);
    }
}

.spinning {
    font-weight: bolder;
    width: 45px;
    animation: spin 2s linear infinite; /* Adjust the animation duration as needed */
}