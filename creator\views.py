
from django.shortcuts import render, redirect, get_object_or_404
from django.http import HttpResponse, JsonResponse
from django.contrib import messages
from django.core.paginator import Paginator
from django.db.models import Q

from .models import Category, Story, UserProfile
from .utils import generate_story


def category_list(request):
    """
    Display a list of all categories with pagination.
    """
    categories = Category.objects.all().order_by('name')
    paginator = Paginator(categories, 10)  # Show 10 categories per page
    page_number = request.GET.get('page')
    page_obj = paginator.get_page(page_number)

    context = {
        'page_obj': page_obj,
        'categories': page_obj,
    }
    return render(request, 'creator/category_list.html', context)


def category_detail(request, category_id):
    """
    Display details of a specific category including its stories and user profiles.
    """
    category = get_object_or_404(Category, id=category_id)
    stories = Story.objects.filter(category=category).order_by('-created')
    user_profiles = UserProfile.objects.filter(category=category).order_by('-created')

    context = {
        'category': category,
        'stories': stories,
        'user_profiles': user_profiles,
    }
    return render(request, 'creator/category_detail.html', context)


def story_list(request):
    """
    Display a list of all stories with search and filtering capabilities.
    """
    stories = Story.objects.all().order_by('-created')

    # Search functionality
    search_query = request.GET.get('search')
    if search_query:
        stories = stories.filter(
            Q(title__icontains=search_query) |
            Q(script__icontains=search_query)
        )

    # Category filtering
    category_id = request.GET.get('category')
    if category_id:
        stories = stories.filter(category_id=category_id)

    # Pagination
    paginator = Paginator(stories, 12)  # Show 12 stories per page
    page_number = request.GET.get('page')
    page_obj = paginator.get_page(page_number)

    # Get all categories for filter dropdown
    categories = Category.objects.all().order_by('name')

    context = {
        'page_obj': page_obj,
        'stories': page_obj,
        'categories': categories,
        'search_query': search_query,
        'selected_category': category_id,
    }
    return render(request, 'creator/story_list.html', context)


def story_detail(request, story_id):
    """
    Display details of a specific story.
    """
    story = get_object_or_404(Story, id=story_id)

    context = {
        'story': story,
    }
    return render(request, 'creator/story_detail.html', context)


def story_create(request):
    """
    Create a new story with AI generation.
    """
    if request.method == 'POST':
        title = request.POST.get('title', '').strip()
        script = request.POST.get('script', '').strip()
        category_id = request.POST.get('category')

        if not title or not script:
            messages.error(request, 'Title and script are required.')
            return redirect('creator:story_create')

        try:
            category = None
            if category_id:
                category = Category.objects.get(id=category_id)

            # Create the story
            story = Story.objects.create(
                title=title,
                script=script,
                category=category
            )

            # Generate video content asynchronously
            try:
                generate_story(script)
                messages.success(request, f'Story "{title}" created and video generation started.')
            except Exception as e:
                messages.warning(request, f'Story created but video generation failed: {str(e)}')

            return redirect('creator:story_detail', story_id=story.id)

        except Category.DoesNotExist:
            messages.error(request, 'Invalid category selected.')
        except Exception as e:
            messages.error(request, f'Error creating story: {str(e)}')

    categories = Category.objects.all().order_by('name')
    context = {
        'categories': categories,
    }
    return render(request, 'creator/story_create.html', context)


def user_profile_list(request):
    """
    Display a list of all user profiles.
    """
    profiles = UserProfile.objects.all().order_by('-created')

    # Category filtering
    category_id = request.GET.get('category')
    if category_id:
        profiles = profiles.filter(category_id=category_id)

    # Pagination
    paginator = Paginator(profiles, 15)  # Show 15 profiles per page
    page_number = request.GET.get('page')
    page_obj = paginator.get_page(page_number)

    # Get all categories for filter dropdown
    categories = Category.objects.all().order_by('name')

    context = {
        'page_obj': page_obj,
        'profiles': page_obj,
        'categories': categories,
        'selected_category': category_id,
    }
    return render(request, 'creator/user_profile_list.html', context)


def dashboard(request):
    """
    Creator dashboard with overview statistics.
    """
    total_categories = Category.objects.count()
    total_stories = Story.objects.count()
    total_profiles = UserProfile.objects.count()
    recent_stories = Story.objects.order_by('-created')[:5]

    context = {
        'total_categories': total_categories,
        'total_stories': total_stories,
        'total_profiles': total_profiles,
        'recent_stories': recent_stories,
    }
    return render(request, 'creator/dashboard.html', context)
