# VIGEN - Software Design Document

## 1. Introduction

### 1.1 Purpose
This document describes the software design for VIGEN, a Django-based video generation platform that automates content creation from podcast sources using AI-powered transcription and video processing capabilities.

### 1.2 Scope
VIGEN enables users to:
- Download and process YouTube videos
- Extract specific segments from videos
- Generate AI-powered transcriptions and subtitles
- Create composite videos with logos and branding
- Manage content categories and accounts
- Process videos asynchronously using background tasks

### 1.3 Definitions and Acronyms
- **VIGEN**: Video Generation Platform
- **SDD**: Software Design Document
- **API**: Application Programming Interface
- **ORM**: Object-Relational Mapping
- **CRUD**: Create, Read, Update, Delete
- **TTS**: Text-to-Speech
- **AI**: Artificial Intelligence

## 2. System Overview

### 2.1 System Architecture
VIGEN follows a modular Django architecture with the following key components:

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   Frontend      │    │   Backend       │    │   External      │
│   (HTMX/HTML)   │◄──►│   (Django)      │◄──►│   Services      │
└─────────────────┘    └─────────────────┘    └─────────────────┘
                              │
                              ▼
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   Database      │    │   Task Queue    │    │   Media Storage │
│   (SQLite)      │    │   (Celery)      │    │   (Local Files) │
└─────────────────┘    └─────────────────┘    └─────────────────┘
```

### 2.2 Technology Stack
- **Backend Framework**: Django 4.2.5
- **Database**: SQLite (development), PostgreSQL (production ready)
- **Task Queue**: Celery with Redis broker
- **Video Processing**: MoviePy, FFmpeg
- **AI Services**: AssemblyAI (transcription), ElevenLabs (TTS)
- **Containerization**: Docker with docker-compose
- **Frontend**: HTMX for dynamic interactions

## 3. System Architecture

### 3.1 High-Level Architecture

```
┌─────────────────────────────────────────────────────────────────┐
│                        VIGEN Platform                          │
├─────────────────────────────────────────────────────────────────┤
│  Presentation Layer                                             │
│  ┌─────────────┐  ┌─────────────┐  ┌─────────────┐            │
│  │   Web UI    │  │   Admin     │  │   API       │            │
│  │   (HTMX)    │  │   Panel     │  │   (Future)  │            │
│  └─────────────┘  └─────────────┘  └─────────────┘            │
├─────────────────────────────────────────────────────────────────┤
│  Business Logic Layer                                          │
│  ┌─────────────┐  ┌─────────────┐  ┌─────────────┐            │
│  │   Website   │  │   Creator   │  │   Task      │            │
│  │   Module    │  │   Module    │  │   Processing│            │
│  └─────────────┘  └─────────────┘  └─────────────┘            │
├─────────────────────────────────────────────────────────────────┤
│  Data Access Layer                                             │
│  ┌─────────────┐  ┌─────────────┐  ┌─────────────┐            │
│  │   Django    │  │   File      │  │   Cache     │            │
│  │   ORM       │  │   System    │  │   (Redis)   │            │
│  └─────────────┘  └─────────────┘  └─────────────┘            │
├─────────────────────────────────────────────────────────────────┤
│  External Services                                             │
│  ┌─────────────┐  ┌─────────────┐  ┌─────────────┐            │
│  │  YouTube    │  │ AssemblyAI  │  │ ElevenLabs  │            │
│  │  Download   │  │ (Speech)    │  │ (TTS)       │            │
│  └─────────────┘  └─────────────┘  └─────────────┘            │
└─────────────────────────────────────────────────────────────────┘
```

### 3.2 Module Architecture

#### 3.2.1 Website Module
- **Purpose**: Core web application functionality
- **Components**:
  - Models: Account, Podcast, PodcastEntry
  - Views: Home, accounts management, history
  - Forms: Input validation and user interaction
  - Tasks: Asynchronous video processing

#### 3.2.2 Creator Module  
- **Purpose**: Content creation and utility functions
- **Components**:
  - Models: Category, UserProfile, Story
  - Utils: Video processing, AI integration
  - Services: Story generation, media manipulation

## 4. Data Design

### 4.1 Entity Relationship Diagram

```
┌─────────────┐     ┌─────────────┐     ┌─────────────┐
│  Category   │────►│   Account   │     │   Story     │
│             │     │             │◄────│             │
│ - id        │     │ - id        │     │ - id        │
│ - name      │     │ - username  │     │ - title     │
│ - created   │     │ - logo      │     │ - script    │
│ - updated   │     │ - created   │     │ - created   │
└─────────────┘     │ - updated   │     │ - updated   │
       │            └─────────────┘     └─────────────┘
       │                   │
       ▼                   │
┌─────────────┐           │
│   Podcast   │           │
│             │           │
│ - id        │           │
│ - source    │           │
│ - title     │           │
│ - duration  │           │
│ - created   │           │
│ - updated   │           │
└─────────────┘           │
       │                   │
       ▼                   │
┌─────────────┐           │
│PodcastEntry │           │
│             │           │
│ - id        │           │
│ - start_time│           │
│ - end_time  │           │
│ - completed │           │
│ - processing│           │
│ - locations │           │
│ - created   │           │
│ - updated   │           │
└─────────────┘
```

### 4.2 Database Schema

#### 4.2.1 Core Tables

**Categories**
- Primary key: id (AutoField)
- Fields: name (CharField), created, updated
- Indexes: name
- Purpose: Organize content by type

**Accounts** 
- Primary key: id (AutoField)
- Foreign keys: category_id
- Fields: username (unique), logo (ImageField)
- Indexes: username, category
- Purpose: User account management with branding

**Podcasts**
- Primary key: id (AutoField) 
- Foreign keys: category_id
- Fields: source (URL), title, description, duration, views
- Indexes: source, category, created
- Purpose: YouTube video metadata storage

**PodcastEntries**
- Primary key: id (AutoField)
- Foreign keys: podcast_id
- Fields: start_time, end_time, completed, processing, locations
- Indexes: podcast, completed, processing, created
- Purpose: Video segment processing tracking

## 5. Component Design

### 5.1 Core Components

#### 5.1.1 Video Processing Pipeline

```python
class VideoProcessor:
    """
    Handles video download, processing, and transformation
    """
    def download_video(url: str) -> str
    def extract_segment(video_path: str, start: int, end: int) -> str
    def add_logo(video_path: str, logo_path: str) -> str
    def generate_subtitles(video_path: str) -> str
    def crop_to_vertical(video_path: str) -> str
```

#### 5.1.2 AI Integration Services

```python
class AIServices:
    """
    Integrates with external AI services
    """
    def transcribe_audio(audio_path: str) -> dict
    def generate_speech(text: str, voice: str) -> str
    def generate_story(prompt: str) -> str
```

#### 5.1.3 Task Management

```python
class TaskManager:
    """
    Manages asynchronous video processing tasks
    """
    def process_video_entry(entry_id: int) -> None
    def cleanup_failed_tasks() -> None
    def monitor_task_progress(task_id: str) -> dict
```

### 5.2 Service Layer Architecture

#### 5.2.1 Video Service
- **Responsibility**: Video download and processing
- **Dependencies**: MoviePy, FFmpeg, PyTube
- **Methods**:
  - `download_from_youtube(url)`
  - `extract_segment(video, start_time, end_time)`
  - `apply_logo_overlay(video, logo)`
  - `convert_to_vertical_format(video)`

#### 5.2.2 Transcription Service
- **Responsibility**: Audio transcription and subtitle generation
- **Dependencies**: AssemblyAI API
- **Methods**:
  - `transcribe_video(video_path)`
  - `generate_srt_file(transcription)`
  - `extract_audio_from_video(video_path)`

#### 5.2.3 Content Generation Service
- **Responsibility**: AI-powered content creation
- **Dependencies**: ElevenLabs API
- **Methods**:
  - `generate_voiceover(text, voice_settings)`
  - `create_story_script(topic, style)`
  - `optimize_content_for_platform(content, platform)`

## 6. Interface Design

### 6.1 User Interface Components

#### 6.1.1 Main Dashboard
- **Purpose**: Central hub for content management
- **Features**:
  - Account selection and management
  - Recent podcast entries
  - Processing status overview
  - Quick actions (add podcast, view history)

#### 6.1.2 Video Processing Interface
- **Purpose**: Configure and monitor video processing
- **Features**:
  - YouTube URL input with validation
  - Time segment selection (start/end times)
  - Processing progress indicators
  - Preview and download options

#### 6.1.3 Account Management
- **Purpose**: Manage user accounts and branding
- **Features**:
  - Account creation and editing
  - Logo upload and management
  - Category assignment
  - Account statistics and analytics

### 6.2 API Design (Future Enhancement)

#### 6.2.1 RESTful Endpoints
```
GET    /api/v1/accounts/           # List accounts
POST   /api/v1/accounts/           # Create account
GET    /api/v1/accounts/{id}/      # Get account details
PUT    /api/v1/accounts/{id}/      # Update account
DELETE /api/v1/accounts/{id}/      # Delete account

GET    /api/v1/podcasts/           # List podcasts
POST   /api/v1/podcasts/           # Create podcast
GET    /api/v1/podcasts/{id}/      # Get podcast details

GET    /api/v1/entries/            # List podcast entries
POST   /api/v1/entries/            # Create entry
GET    /api/v1/entries/{id}/       # Get entry details
PUT    /api/v1/entries/{id}/       # Update entry status
```

#### 6.2.2 WebSocket Endpoints (Future)
```
/ws/processing/{entry_id}/         # Real-time processing updates
/ws/notifications/                 # System notifications
```

## 7. Processing Workflows

### 7.1 Video Processing Workflow

```mermaid
graph TD
    A[User submits YouTube URL] --> B[Validate URL]
    B --> C[Download video]
    C --> D[Extract metadata]
    D --> E[Create podcast entry]
    E --> F[Queue processing task]
    F --> G[Extract video segment]
    G --> H[Apply logo overlay]
    H --> I[Convert to vertical format]
    I --> J[Generate transcription]
    J --> K[Create subtitle file]
    K --> L[Save processed video]
    L --> M[Update entry status]
    M --> N[Notify user completion]
```

### 7.2 Error Handling Workflow

```mermaid
graph TD
    A[Task execution] --> B{Error occurs?}
    B -->|No| C[Complete successfully]
    B -->|Yes| D[Log error details]
    D --> E[Retry task]
    E --> F{Max retries reached?}
    F -->|No| A
    F -->|Yes| G[Mark as failed]
    G --> H[Notify administrators]
    H --> I[Clean up resources]
```

## 8. Security Design

### 8.1 Security Measures

#### 8.1.1 Input Validation
- URL validation for YouTube links
- File type validation for uploads
- Size limits for media files
- CSRF protection on all forms
- XSS prevention through template escaping

#### 8.1.2 Authentication & Authorization
- Django's built-in authentication system
- Admin panel access control
- Session management
- Password hashing (PBKDF2)

#### 8.1.3 File Security
- Secure file upload handling
- Path traversal prevention
- Media file access control
- Temporary file cleanup

### 8.2 Data Protection
- Database connection security
- Sensitive data handling (API keys)
- Audit logging for critical operations
- Backup and recovery procedures

## 9. Performance Considerations

### 9.1 Optimization Strategies

#### 9.1.1 Database Optimization
- Proper indexing on frequently queried fields
- Query optimization with select_related/prefetch_related
- Database connection pooling
- Pagination for large datasets

#### 9.1.2 Caching Strategy
- Redis-based caching for frequently accessed data
- Template fragment caching
- Static file caching
- API response caching (future)

#### 9.1.3 Media Processing Optimization
- Asynchronous task processing with Celery
- Video compression optimization
- Parallel processing for multiple segments
- Resource cleanup after processing

### 9.2 Scalability Design
- Horizontal scaling capability with load balancers
- Database sharding considerations
- CDN integration for media delivery
- Microservices migration path

## 10. Deployment Architecture

### 10.1 Container Architecture

```yaml
services:
  web:
    - Django application server
    - Static file serving
    - Health check endpoints

  redis:
    - Caching layer
    - Celery message broker
    - Session storage

  celery:
    - Background task processing
    - Video processing workers
    - Periodic task scheduling
```

### 10.2 Environment Configuration
- Development: SQLite, local Redis
- Staging: PostgreSQL, Redis cluster
- Production: PostgreSQL cluster, Redis cluster, CDN

## 11. Monitoring and Logging

### 11.1 Application Monitoring
- Health check endpoints
- Performance metrics collection
- Error rate monitoring
- Resource usage tracking

### 11.2 Logging Strategy
- Structured logging with JSON format
- Log levels: DEBUG, INFO, WARNING, ERROR, CRITICAL
- Centralized log aggregation
- Log rotation and retention policies

## 12. Future Enhancements

### 12.1 Planned Features
- RESTful API development
- Real-time processing updates via WebSockets
- Advanced video editing capabilities
- Multi-platform content optimization
- Analytics and reporting dashboard

### 12.2 Technical Improvements
- Migration to microservices architecture
- Implementation of event-driven architecture
- Advanced caching strategies
- Machine learning integration for content optimization
- Automated testing and CI/CD pipeline

## 13. Conclusion

VIGEN represents a comprehensive video generation platform built on modern web technologies. The modular Django architecture provides flexibility for future enhancements while maintaining code quality and performance. The system's design emphasizes scalability, security, and user experience, making it suitable for both personal and enterprise use cases.

The asynchronous processing capabilities ensure responsive user interactions while handling resource-intensive video processing tasks in the background. The integration with AI services positions VIGEN at the forefront of automated content creation technology.
