# Generated by Django 4.2.5 on 2023-11-07 23:57

from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        ("creator", "0004_alter_category_options_alter_story_options_and_more"),
        ("website", "0001_initial"),
    ]

    operations = [
        migrations.AddField(
            model_name="podcast",
            name="discription",
            field=models.TextField(blank=True, null=True),
        ),
        migrations.AddField(
            model_name="podcast",
            name="duration",
            field=models.CharField(blank=True, max_length=250, null=True),
        ),
        migrations.AddField(
            model_name="podcast",
            name="image_source",
            field=models.CharField(blank=True, max_length=255, null=True),
        ),
        migrations.AddField(
            model_name="podcast",
            name="views",
            field=models.BigIntegerField(blank=True, null=True),
        ),
        migrations.AlterField(
            model_name="podcast",
            name="category",
            field=models.ForeignKey(
                blank=True,
                on_delete=django.db.models.deletion.DO_NOTHING,
                to="creator.category",
            ),
        ),
        migrations.AlterField(
            model_name="podcast",
            name="title",
            field=models.CharField(blank=True, max_length=200, null=True),
        ),
        migrations.AlterField(
            model_name="podcastentry",
            name="podcast",
            field=models.ForeignKey(
                on_delete=django.db.models.deletion.DO_NOTHING, to="website.podcast"
            ),
        ),
    ]
