"""
Test suite for video processing functionality in the creator module.

This module contains comprehensive tests for the video processing pipeline
including video download, processing, transcription, and AI integration.
"""

import os
import tempfile
import unittest
from unittest.mock import patch, MagicMock, mock_open
from django.test import TestCase
from django.conf import settings

from creator.utils import (
    count_files_in_folder,
    select_random_video,
    get_random_clip_from_videos,
    crop_9_16,
    crop_video,
    save_final_video,
    generate_voiceover_from_text,
    clip_youtube_and_subtitles,
    _clip_subtitle_file
)
from creator.models import Category, Story, UserProfile


class VideoProcessingUtilsTestCase(TestCase):
    """Test case for video processing utility functions."""

    def setUp(self):
        """Set up test fixtures."""
        self.test_dir = tempfile.mkdtemp()
        self.test_video_path = os.path.join(self.test_dir, 'test_video.mp4')
        self.test_subtitle_path = os.path.join(self.test_dir, 'test_subtitle.srt')
        
        # Create test category
        self.test_category = Category.objects.create(name='Test Category')

    def tearDown(self):
        """Clean up test fixtures."""
        import shutil
        if os.path.exists(self.test_dir):
            shutil.rmtree(self.test_dir)

    def test_count_files_in_folder_valid_directory(self):
        """Test counting files in a valid directory."""
        # Create test files
        for i in range(3):
            with open(os.path.join(self.test_dir, f'file_{i}.txt'), 'w') as f:
                f.write('test content')
        
        result = count_files_in_folder(self.test_dir)
        self.assertEqual(result, 3)

    def test_count_files_in_folder_empty_directory(self):
        """Test counting files in an empty directory."""
        result = count_files_in_folder(self.test_dir)
        self.assertEqual(result, 0)

    def test_count_files_in_folder_nonexistent_directory(self):
        """Test counting files in a non-existent directory."""
        result = count_files_in_folder('/nonexistent/path')
        self.assertEqual(result, 0)

    @patch('creator.utils.os.path.exists')
    @patch('creator.utils.os.listdir')
    def test_select_random_video_success(self, mock_listdir, mock_exists):
        """Test successful random video selection."""
        mock_exists.return_value = True
        mock_listdir.return_value = ['video1.mp4', 'video2.avi', 'document.txt']
        
        with patch('creator.utils.random.choice') as mock_choice:
            mock_choice.return_value = 'video1.mp4'
            with patch('creator.utils.os.path.isfile', return_value=True):
                result = select_random_video()
                self.assertIsNotNone(result)
                self.assertTrue(result.endswith('video1.mp4'))

    @patch('creator.utils.os.path.exists')
    def test_select_random_video_no_directory(self, mock_exists):
        """Test random video selection when directory doesn't exist."""
        mock_exists.return_value = False
        result = select_random_video()
        self.assertIsNone(result)

    @patch('creator.utils.os.path.exists')
    @patch('creator.utils.os.listdir')
    def test_select_random_video_no_videos(self, mock_listdir, mock_exists):
        """Test random video selection when no video files exist."""
        mock_exists.return_value = True
        mock_listdir.return_value = ['document.txt', 'image.jpg']
        
        result = select_random_video()
        self.assertIsNone(result)

    def test_get_random_clip_from_videos_invalid_duration(self):
        """Test get_random_clip_from_videos with invalid duration."""
        with self.assertRaises(ValueError):
            get_random_clip_from_videos(-1)
        
        with self.assertRaises(ValueError):
            get_random_clip_from_videos(0)
        
        with self.assertRaises(ValueError):
            get_random_clip_from_videos(4000)  # Too long
        
        with self.assertRaises(ValueError):
            get_random_clip_from_videos("invalid")

    @patch('creator.utils.select_random_video')
    def test_get_random_clip_from_videos_no_video_found(self, mock_select):
        """Test get_random_clip_from_videos when no video is found."""
        mock_select.return_value = None
        result = get_random_clip_from_videos(10)
        self.assertIsNone(result)

    def test_generate_voiceover_from_text_invalid_input(self):
        """Test generate_voiceover_from_text with invalid inputs."""
        with self.assertRaises(ValueError):
            generate_voiceover_from_text("")
        
        with self.assertRaises(ValueError):
            generate_voiceover_from_text("   ")  # Whitespace only
        
        with self.assertRaises(ValueError):
            generate_voiceover_from_text("x" * 6000)  # Too long
        
        with self.assertRaises(ValueError):
            generate_voiceover_from_text("test", speed=-1)
        
        with self.assertRaises(ValueError):
            generate_voiceover_from_text("test", speed=5)

    @patch('creator.utils.ELEVENLABS_API_KEY', None)
    def test_generate_voiceover_from_text_no_api_key(self):
        """Test generate_voiceover_from_text without API key."""
        with self.assertRaises(ValueError):
            generate_voiceover_from_text("test text")

    def test_crop_video_invalid_input(self):
        """Test crop_video with invalid inputs."""
        mock_clip = MagicMock()
        mock_clip.size = (1920, 1080)
        
        # Test with valid inputs first
        mock_clip.crop.return_value = MagicMock()
        result = crop_video(mock_clip, 800, 600)
        self.assertIsNotNone(result)
        mock_clip.crop.assert_called_once()

    def test_crop_9_16_invalid_dimensions(self):
        """Test crop_9_16 with invalid clip dimensions."""
        mock_clip = MagicMock()
        mock_clip.size = (-100, -200)  # Invalid dimensions
        
        result = crop_9_16(mock_clip)
        self.assertIsNone(result)

    def test_crop_9_16_valid_clip(self):
        """Test crop_9_16 with valid clip."""
        mock_clip = MagicMock()
        mock_clip.size = (1920, 1080)
        mock_clip.crop.return_value = MagicMock()
        
        result = crop_9_16(mock_clip)
        self.assertIsNotNone(result)
        mock_clip.crop.assert_called_once()

    def test_save_final_video_none_input(self):
        """Test save_final_video with None input."""
        with self.assertRaises(ValueError):
            save_final_video(None)

    @patch('creator.utils.os.makedirs')
    @patch('creator.utils.count_files_in_folder')
    def test_save_final_video_success(self, mock_count, mock_makedirs):
        """Test successful video saving."""
        mock_count.return_value = 5
        mock_video = MagicMock()
        mock_video.write_videofile = MagicMock()
        mock_video.close = MagicMock()
        
        result = save_final_video(mock_video, self.test_dir)
        self.assertIsNotNone(result)
        self.assertTrue(result.endswith('5.mp4'))
        mock_video.write_videofile.assert_called_once()
        mock_video.close.assert_called_once()


class VideoClippingTestCase(TestCase):
    """Test case for video clipping functionality."""

    def setUp(self):
        """Set up test fixtures."""
        self.test_dir = tempfile.mkdtemp()
        self.test_subtitle_content = """1
00:00:00,000 --> 00:00:05,000
First subtitle

2
00:00:05,000 --> 00:00:10,000
Second subtitle

3
00:00:10,000 --> 00:00:15,000
Third subtitle
"""

    def tearDown(self):
        """Clean up test fixtures."""
        import shutil
        if os.path.exists(self.test_dir):
            shutil.rmtree(self.test_dir)

    def test_clip_subtitle_file_valid_input(self):
        """Test subtitle clipping with valid input."""
        subtitle_path = os.path.join(self.test_dir, 'test.srt')
        with open(subtitle_path, 'w', encoding='utf-8') as f:
            f.write(self.test_subtitle_content)
        
        result = _clip_subtitle_file(subtitle_path, 2, 8)
        self.assertIsNotNone(result)
        
        # Verify the clipped subtitle file exists and has content
        self.assertTrue(os.path.exists(result))
        with open(result, 'r', encoding='utf-8') as f:
            content = f.read()
            self.assertIn('First subtitle', content)

    def test_clip_subtitle_file_nonexistent_file(self):
        """Test subtitle clipping with non-existent file."""
        result = _clip_subtitle_file('/nonexistent/file.srt', 0, 10)
        self.assertIsNone(result)

    @patch('creator.utils.VideoFileClip')
    def test_clip_youtube_and_subtitles_success(self, mock_video_clip):
        """Test successful YouTube video clipping."""
        mock_clip = MagicMock()
        mock_clip.duration = 100
        mock_clip.subclip.return_value = MagicMock()
        mock_video_clip.return_value = mock_clip
        
        # Create test subtitle file
        subtitle_path = os.path.join(self.test_dir, 'test.srt')
        with open(subtitle_path, 'w', encoding='utf-8') as f:
            f.write(self.test_subtitle_content)
        
        video_clip, subtitle_clip = clip_youtube_and_subtitles(
            'test_video.mp4', 5, 15, subtitle_path
        )
        
        self.assertIsNotNone(video_clip)
        self.assertIsNotNone(subtitle_clip)
        mock_clip.subclip.assert_called_once_with(5, 15)

    @patch('creator.utils.VideoFileClip')
    def test_clip_youtube_and_subtitles_invalid_times(self, mock_video_clip):
        """Test YouTube clipping with invalid time parameters."""
        mock_clip = MagicMock()
        mock_clip.duration = 100
        mock_video_clip.return_value = mock_clip
        
        # Test start time >= end time
        video_clip, subtitle_clip = clip_youtube_and_subtitles(
            'test_video.mp4', 15, 5
        )
        
        self.assertIsNone(video_clip)
        self.assertIsNone(subtitle_clip)


class ModelTestCase(TestCase):
    """Test case for creator models."""

    def test_category_creation(self):
        """Test category model creation."""
        category = Category.objects.create(name='Test Category')
        self.assertEqual(str(category), 'Test Category')
        self.assertEqual(category.name, 'Test Category')

    def test_story_creation(self):
        """Test story model creation."""
        category = Category.objects.create(name='Test Category')
        story = Story.objects.create(
            title='Test Story',
            script='This is a test story script.',
            category=category
        )
        self.assertEqual(str(story), 'Test Story')
        self.assertEqual(story.category, category)

    def test_user_profile_creation(self):
        """Test user profile model creation."""
        category = Category.objects.create(name='Test Category')
        profile = UserProfile.objects.create(
            username='testuser',
            category=category
        )
        self.assertEqual(str(profile), 'testuser')
        self.assertEqual(profile.category, category)

    @patch('creator.models.os.makedirs')
    @patch('creator.models.os.path.exists')
    def test_category_directory_creation(self, mock_exists, mock_makedirs):
        """Test category directory structure creation."""
        mock_exists.return_value = False
        category = Category.objects.create(name='Test Category')
        
        # The save method should trigger directory creation
        self.assertTrue(mock_makedirs.called)


if __name__ == '__main__':
    unittest.main()
