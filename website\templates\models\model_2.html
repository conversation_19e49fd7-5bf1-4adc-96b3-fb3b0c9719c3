<h6 class="text-center text-white fs-4">Model II - Story Generator</h6>

<form hx-post="{% url "website:create-story" %}" hx-indicator="#waiting" hx-swap="none" class="custom-form mt-4 pt-2 mb-lg-0 mb-5" role="search" id="story" name="story">
    <div class="text-center text-white mb-4 d-flex justify-content-around">
      <div class="d-flex justify-content-between">
      <h5 class="my-auto">Story Title</h5>
      <input type="text" required name="story-title" class="rounded-pill text-center fw ms-2">
    </div>  
      <div class="fw-bold ">
        <label class="px-2">Category</label>  
        <select name="category" class="py-2 px-4 bg-danger border-danger text-white fw-bold rounded-pill">
          {% for cat in categories %}
          
            <option value="{{cat.name}}">{{cat.name}}</option>
          {% endfor %}
        </select>
      </div>
    </div>
    <div class="input-group input-group-lg">
        
        <div class="form-floating" id="story-form" >
            <textarea class="form-control" for="story" name="story" placeholder="Leave a comment here" required id="floatingTextarea2" style="height: 100px"></textarea>
            <label for="floatingTextarea2">Story to generate: </label>
        </div>
        
    </div>
    <button type="submit" class="trans-focus form-control btn btn-outline-danger fw-bold text-white border-5">Generate Story</button>
    
</form>  
