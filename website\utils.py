from pytubefix import YouTube
import yt_dlp


def get_clip_thumbnail_and_title(clip_url):
    yt = YouTube(clip_url)
    return yt.title, yt.thumbnail_url

def count_seconds(duration):
    try:
        duration = duration.split(":")
        if len(duration) == 2:
            return int(duration[0])*60 + int(duration[1])
        elif len(duration) == 3:
            return int(duration[0])*3600 + int(duration[1])*60 + int(duration[2])
        else:
            return 0
    except ValueError as e:
        pass

def get_youtube_captions(video_url, language_code='en'):
    ydl_opts = {
        'writesubtitles': True,
        'skip_download': True,
        'subtitlesformat': 'srt',
        'subtitleslangs': [language_code],
        'quiet': True,
        'outtmpl': '-',
        'socket_timeout': 60,  # Increase timeout to 60 seconds
    }

    with yt_dlp.YoutubeDL(ydl_opts) as ydl:
        result = ydl.extract_info(video_url, download=False)
        subtitles = result.get('requested_subtitles')
        if subtitles and language_code in subtitles:
            srt_file = subtitles[language_code]['url']
            return ydl.urlopen(srt_file).read().decode('utf-8')
        else:
            print(f"No captions found with yt-dlp for language: {language_code}")


