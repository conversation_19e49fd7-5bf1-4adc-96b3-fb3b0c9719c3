function setActiveNavLink(event) {


    // Remove the 'active' class from all nav links within the navigation bar
    const navLinks = document.querySelectorAll('.navbar .nav-link');
    navLinks.forEach((link) => {
        link.classList.remove('active');
    });

    // Add the 'active' class to the clicked nav link
    event.target.classList.add('active');
}

function setActiveModelsNavLink(event) {

    // Remove the 'active' class from all nav links within the navigation bar with the ID "models"
    const navLinks = document.querySelectorAll('#models .nav-link');
    navLinks.forEach((link) => {
        link.classList.remove('active');
    });

    // Add the 'active' class to the clicked nav link
    event.target.classList.add('active');
}

document.addEventListener('DOMContentLoaded', function () {
    const navLinks = document.querySelectorAll('#models .nav-link');
    navLinks.forEach((link) => {
        link.addEventListener('click', setActiveModelsNavLink);
    });
});


function checkAndClick() {
    // Get the value of the selected option in the "pod-category" select element
    var selectValue = document.getElementById("pod-category").value;

    // Check if the selected value is not empty
    if (selectValue.trim() !== "") {
        // Perform an htmx post directly
        htmx.post("");
    } else {
        // If empty, you can handle this case (e.g., show an alert)
        alert("Please select a valid option");
    }
}

// Handle the HTMX response before the swap event
document.addEventListener('htmx:beforeSwap', function(event) {
    // Check if the target is the #status-container
    if (event.target.id === 'status-container') {
        const responseText = event.detail.xhr.responseText.trim();

        // If the response is empty, prevent the swap
        if (responseText === '') {
            event.preventDefault(); // Prevent HTMX from performing the swap
            console.log('Processing, spinner remains.');
        }
    }
});