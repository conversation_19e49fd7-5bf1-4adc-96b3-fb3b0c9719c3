# VIGEN Environment Configuration Template
# Copy this file to .env and fill in your actual values

# Django Settings
DEBUG=1
SECRET_KEY=your-secret-key-here
ALLOWED_HOSTS=localhost,127.0.0.1

# Database Configuration (for production)
# DATABASE_URL=postgresql://user:password@localhost:5432/vigen_db

# Redis Configuration
CELERY_BROKER_REDIS_URL=redis://redis:6379/0

# API Keys (External Services)
ELEVENLABS_API_KEY=your-elevenlabs-api-key-here
ASSEMBLYAI_API_KEY=your-assemblyai-api-key-here

# Media and Static Files
MEDIA_ROOT=/app/mediafiles
STATIC_ROOT=/app/staticfiles

# ImageMagick Configuration
IMAGEMAGICK_BINARY=/usr/bin/convert

# Logging Configuration
LOG_LEVEL=INFO
LOG_FILE=/app/logs/django.log

# Security Settings (for production)
# SECURE_SSL_REDIRECT=True
# SECURE_HSTS_SECONDS=31536000
# SECURE_HSTS_INCLUDE_SUBDOMAINS=True
# SECURE_HSTS_PRELOAD=True
# SESSION_COOKIE_SECURE=True
# CSRF_COOKIE_SECURE=True
